<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { IconifyIcon } from "@vben/icons";
import { AgGridVue } from 'ag-grid-vue3';
import { ElButton, ElCard, ElNotification } from 'element-plus';
import { type GridApi, type GridOptions, AllCommunityModule } from 'ag-grid-community';
import { sysRoleUser, deleteUserRole } from '#/api/sys/role';

import addUserModel from './addUserRole.vue';
import ActionCell from './ButtonRenderer.vue';

const record = ref<{ id?: number } | null>(null);
const gridApi = ref<GridApi | null>(null);
const isDrawerOpen = ref(false);
const addUserRef = ref();

const [FormQuery] = useVbenForm({
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  collapsed: true,
  submitButtonOptions: {
    content: '查询',
  },
  commonConfig: {
    labelWidth: 60,
  },
  schema: [
    {
      component: 'Input',
      fieldName: 'accound',
      label: '用户名',
      componentProps: {
        placeholder: '',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '姓名',
      componentProps: {
        placeholder: '',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'telePhone',
      label: '手机号码',
      componentProps: {
        placeholder: '请输入手机号码',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'email',
      label: '邮件',
      componentProps: {
        placeholder: '',
        allowClear: true,
      },
    },
  ],
});
const components = {
  actionCell: ActionCell, // 这里的 key 是你在 cellRenderer 中使用的值
};
const columnDefs = [
  { headerName: "真实姓名", field: "name" },
  { headerName: "账号", field: "account" },
  // { headerName: "手机号", field: "telephone" },
  // { headerName: "邮箱", field: "email" },
  { headerName: "状态", field: "status" },
  { headerName: " 备注", field: "remark" },
  { headerName: "操作",
      field: "action", // 注意这里field不是实际的数据字段，仅用于标识
      pinned: 'right' as const,
      cellRenderer: 'actionCell',
      width: 100,
      cellRendererParams: {
        actions: [
          {
            label: '删除',
            callback: (data: any) => {
              deleteUserRole({id:data.id});
              ElNotification({duration: 2500,message: '刪除成功',type: 'success'});
              getData();
            },
            type: 'danger',
            size: 'small',
          },
        ]
      }
  },
];

const defaultColDef = {
  flex: 1,
};

const gridOptions: GridOptions = {
  columnDefs: columnDefs,
  rowData: [],
  defaultColDef: defaultColDef,
  pagination: true,
  paginationPageSizeSelector: [20, 50, 100],
  rowModelType: 'clientSide',
  onGridReady: (params) => {
    gridApi.value = params.api;
    getData();
  },
};

const modules = [AllCommunityModule];

const [Drawer, DrawerApi] = useVbenDrawer({
  showConfirmButton :false,
  onOpenChange(isOpen) {
    if (isOpen) {
      record.value = isOpen ? DrawerApi.getData()?.record : {};
      isDrawerOpen.value = true;
    }
  },
  onConfirm() {
    // 确认执行操作
  },
});

async function getData() {
  try {
    const res = await sysRoleUser({ roleId: record.value?.id });
    if (Array.isArray(res) && gridApi.value) {
      gridApi.value.setGridOption("rowData", res);
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
}

const handleAdd = () => {
  addUserRef.value.setData({
    record: record.value,
  });
  addUserRef.value.open();
  addUserRef.value.onClosed = () => {
    // 调用刷新数据的方法
    getData();
  };
};


defineExpose(DrawerApi);
</script>

<template>
  <div>
    <Drawer class="w-[50%]" title="成员管理">
      <ElCard>
        <FormQuery />
        <ElButton type="primary" @click="handleAdd">
          <IconifyIcon icon="material-symbols:add-2-rounded" />
          添加用户
        </ElButton>
        <div style="margin-top: 20px;">
          <AgGridVue :gridOptions="gridOptions" class="ag-theme-alpine" style="width: 100%; height: 70vh;"
          :components="components" :modules="modules">
          </AgGridVue>
        </div>
      </ElCard>
    </Drawer>
  </div>
  <div>
    <addUserModel ref="addUserRef" />
  </div>
</template>
