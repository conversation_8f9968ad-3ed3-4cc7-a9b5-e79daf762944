<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import {
  ElCard,
  ElMessage,
  ElMessageBox,
  ElNotification
  
} from 'element-plus';
import { AgGridVue } from 'ag-grid-vue3';
import { type GridApi, type GridOptions, AllCommunityModule } from 'ag-grid-community';

import { getNotRoleUser,insertRoleUser } from '#/api/sys/role';

const gridApi = ref<GridApi | null>(null);
const record = ref();
const [Modal, modalApi] = useVbenModal({
  closeOnClickModal:false,
  draggable: true,
  onOpenChange(isOpen) {
    record.value = isOpen ? modalApi.getData()?.record || {} : {};

  },
  onCancel() {
    modalApi.close();
  },
  onConfirm(){
    ElMessageBox.confirm(
    '确定要保存当前选择的用户吗?',
    'Warning',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      //获取当前勾选的行的id
      const selectedRows = gridApi.value?.getSelectedRows();
      //将ID组装成用逗号隔开的string
      const ids = selectedRows?.map((row: any) => row.id).join(',');
      await insertRoleUser({roleId: record.value?.id,items:ids});
      ElNotification({duration: 2500,message: '保存成功',type: 'success'});
      //关闭窗体
      modalApi.close();
    })
    .catch(() => {
      ElNotification({duration: 2500,message: '取消成功',type: 'info'});
    });
  }
});
const [FormQuery] = useVbenForm({
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  collapsed: true,
  submitButtonOptions: {
    content: '查询',
  },
  commonConfig: {
    labelWidth: 60,
  },
  schema: [
    {
      component: 'Input',
      fieldName: 'account',
      label: '用户名',
      componentProps: {
        placeholder: '',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '姓名',
      componentProps: {
        placeholder: '',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'telePhone',
      label: '手机号码',
      componentProps: {
        placeholder: '请输入手机号码',
        allowClear: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'email',
      label: '邮件',
      componentProps: {
        placeholder: '',
        allowClear: true,
      },
    },
  ],
  handleSubmit: onSubmit,
});

function onSubmit(values: Record<string, any>) {
  //console.log(`form values: ${JSON.stringify(values)}`)
  getData(values);
}

const columnDefs = [
  // {
  //   //checkboxSelection: true, // 启用行选择复选框
  //   //headerCheckboxSelection: true, // 启用表头全选复选框
  //   maxWidth: 50
  // },
  { headerName: "真实姓名", field: "name" },
  { headerName: "账号", field: "account" },
  { headerName: "手机号", field: "telephone" },
  { headerName: "邮箱", field: "email" },
  { headerName: "状态", field: "status" },
  { headerName: " 备注", field: "remark" },
];

const defaultColDef = {
  flex: 1,
};

const gridOptions: GridOptions = {
  columnDefs: columnDefs,
  rowData: [],
  rowSelection : { 
    mode: 'multiRow',  // singleRow
    checkboxes:true,
    headerCheckbox : true,
    enableClickSelection: false,
  },
  //rowSelection: { checkboxes: true },  // 新的配置方式
  defaultColDef: defaultColDef,
  pagination: true,
  paginationPageSizeSelector: [20, 50, 100],
  rowModelType: 'clientSide',
  onGridReady: (params) => {
    gridApi.value = params.api;
    getData();
  },
};

async function getData(values?:any | {}) {
  try {
    const res = await getNotRoleUser(
    { ...values,roleId: record.value?.id });
    if (Array.isArray(res) && gridApi.value) {
      gridApi.value.setGridOption("rowData", res);
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
}
const modules = [AllCommunityModule];
defineExpose(modalApi);
</script>
<template>
  <Modal
    class="w-[900px]"
    title="用户新增"
    title-tooltip="请保证角色代码唯一！"
  >

    <div>
      <ElCard>
        <FormQuery />
        <div style="margin-top: 20px;">
          <AgGridVue :gridOptions="gridOptions" class="ag-theme-alpine" style="width: 100%; height: 50vh;"
            :modules="modules">
          </AgGridVue>
        </div>
      </ElCard>
    </div>

    
  </Modal>
</template>
