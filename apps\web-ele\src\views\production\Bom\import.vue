<template>
  <Modal class="w-[60%] h-[100%]" :title="title">
    <div class="mb-4">
      <ElUpload class="upload-demo" drag :show-file-list="false" accept=".xlsx, .xls" :limit="1"
        :on-exceed="handleExceed" :before-upload="handleBeforeUpload">
        <div class="el-upload__text">
          {{ $t('production.drgaFile') }}<em>{{ $t('production.clickUpload') }}</em>
        </div>
        <template #tip>
          <div class="el-upload__tip text-right"> {{ $t('production.importExcel') }}</div>
        </template>
      </ElUpload>
      <!-- 下载模板按钮 -->
    </div>
    <ClientGridComponent ref="gridRef" :columnDefs="columnDefs" :rowData="rowData" :pagination="false" style="height: 78%;"
      :defaultColDef="defaultColDef" :getRowClass="getRowClass" />
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ElUpload } from 'element-plus';

import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import { useVbenModal } from '@vben/common-ui';
import { $t } from '#/locales';
import { useMessage } from '#/components/elementPlus/useMessage';
import { downLoadExcelModel, importTechByExcel, insertBomDataByExcel } from '#/api/production/bom';

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  destroyOnClose:true,
  onOpenChange(isOpen) {
    rowData.value = [];
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    saveData();

  }
});

const saveData = async () => {
  //检查Grid中的数据,是否存在错误信息
  const hasError = rowData.value.some(row => row.errorMsg && row.errorMsg.trim() !== '');

  if (hasError) {
    useMessage().showMessage('error', $t('production.dataContainsErrors'));
    return;
  }
  modalApi.setState({ confirmLoading: true });
  try {
    await insertBomDataByExcel({
      items: JSON.stringify(rowData.value)
    }).then(res => {
      useMessage().showMessage('success', $t('production.SaveSuccess'));
      rowData.value = [];
      modalApi.setState({ confirmLoading: false });
      modalApi.close();
    });
    
    modalApi.close();
  } catch (error) {
    modalApi.setState({ confirmLoading: false });
    console.log(error);
  }
};

const gridRef = ref();
const title = $t('production.Import');
const rowData = ref<any[]>([]);

const columnDefs = [
  // { headerName: '序号', field: 'seq', width: 90 },
  { headerName: $t('production.parentMCode'), field: 'itemMCode', width: 180 },
  { headerName: $t('production.mName'), field: 'seriesNo', width: 135 },
  { headerName: $t('production.SpecModel'), field: 'styleNo', width: 120 },
  { headerName: $t('production.subMCode'), field: 'intCode', width: 170 },
  { headerName: $t('production.subMName'), field: 'mName', width: 170 },
  { headerName: $t('production.subSpec'), field: 'mCode', width: 170 },
  { headerName: $t('production.setsTakenNo'), field: 'casing', width: 100 },
  { headerName: $t('production.UnitConsumption'), field: 'originalUnit', width: 90 },
  { headerName: $t('production.EffectiveUsageRate'), field: 'effectiveUsageRate', width: 130 },
  { headerName: $t('production.MopLength'), field: 'mopLength', width: 115 },
  { headerName: $t('production.errorMsg'), field: 'errorMsg', width: 200 },


];

const defaultColDef = {
  resizable: true,
  sortable: true
};

const getRowClass = (params: { data: { hasErrorMsg?: boolean } }) => {
   // 这里可以自定义逻辑，比如根据某些条件返回类名
   if (params.data.hasErrorMsg) {
    console.log(params.data.hasErrorMsg);
    return 'import-error';
  }
  return ''; // 默认不应用样式
};

const handleExceed = () => {
  useMessage().showMessage('warning', $t('production.importExceed'));
};

// 处理 Excel 上传
const handleBeforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel';

  if (!isExcel) {
    useMessage().showMessage('warning', $t('production.importExcel'));
    return false;
  }
  
  modalApi.lock();
  const formData = new FormData();
  formData.append('inputStream', file);

  importTechByExcel(formData).then((res) => {
    useMessage().showMessage('success', $t('production.importSuccess'));
    rowData.value = res || [];
  }).catch((error) => {
    useMessage().showMessage('error', $t('production.importFailed'));
    console.error('Import failed:', error);
  }).finally(() => {
    modalApi.unlock();
  });
  

  return false; // 阻止 ElUpload 的自动上传
};

const downloadTemplate = () => {
  downLoadExcelModel().then((response) => {

    // 解码 Base64 字符串
    const byteCharacters = atob(response.fileContent);

    // 将 Base64 字符串转换为字节数组
    const byteArrays = [];
    for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
      const slice = byteCharacters.slice(offset, offset + 1024);
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const file = new Blob(byteArrays, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    const a = document.createElement('a');
    a.style.display = 'none';
    const fileURL = window.URL.createObjectURL(file);
    a.href = fileURL;
    a.download = response.fileName;
    document.body.appendChild(a);
    a.click();
    // 释放 URL 对象
    window.URL.revokeObjectURL(fileURL);
    document.body.removeChild(a);
  });
}

defineExpose(modalApi);
</script>
