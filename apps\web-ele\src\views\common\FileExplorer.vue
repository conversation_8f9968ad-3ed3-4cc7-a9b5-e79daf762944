<template>
  <Modal class="file-explorer w-[50%] h-[70%]" :title="title" >
    <div class="file-explorer-container">
      <!-- 左侧目录树 -->
      <div class="directory-tree">
        <div class="tree-header">
          <div class="tree-title">文件目录</div>
          <ElButton size="small" @click="refreshDirectoryTree">
            <ElIcon><ElementPlusIcons.SvgRefresh /></ElIcon>
          </ElButton>
        </div>
        <ElTree
          ref="directoryTreeRef"
          :data="directoryTree"
          :props="{ label: 'name', children: 'children' }"
          node-key="path"
          highlight-current
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
          :default-expanded-keys="['/']">
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <ElIcon>
                <component :is="data.type === 'folder' ? ElementPlusIcons.SvgFolder : ElementPlusIcons.SvgDocument" />
              </ElIcon>
              <span>{{ node.label }}</span>
            </div>
          </template>
        </ElTree>
      </div>

      <!-- 右侧文件列表 -->
      <div class="file-content">
        <div style="display: flex;margin:0.5rem 0;">
      <ElButton
        v-if="currentPath !== '/'"
        @click="navigateBack"
        size="small"
        style="padding: 0 0.5rem;"
      >
        <ElIcon><ElementPlusIcons.ArrowLeft /></ElIcon>
      </ElButton>
      <ElButton
        @click="refreshList"
        size="small"
        style="padding: 0 0.5rem;"
      >
        <ElIcon><ElementPlusIcons.SvgRefresh /></ElIcon>
      </ElButton>
      <div style="align-self:center;margin: 0 0.5rem;">当前目录: </div>
      <div class="path-navigator">
        <ElLink @click="navigateToRoot">
          <ElIcon><ElementPlusIcons.SvgHomeFilled /></ElIcon>
        </ElLink>
        <div style="margin: 0 0.4rem;">/</div>
        <template v-for="(segment, index) in pathSegments" :key="index">
          <ElLink @click="navigateToPath(segment.path)">
            {{ segment.name }}
          </ElLink>
          <div v-if="index < pathSegments.length - 1" style="margin: 0 0.4rem;">/</div>
        </template>
      </div>
    </div>

    <div style="display: flex;margin-bottom: 1rem;">
      <ElButton size="small" @click="createFolder">新建文件夹</ElButton>
      <ElUpload
        ref="fileUploadRef"
        :http-request="handleFileUpload"
        :show-file-list="false"
        multiple
        :accept="'*/*'"
      >
        <ElButton size="small" style="margin-left: 10px">上传文件</ElButton>
      </ElUpload>
      <ElUpload
        ref="folderUploadRef"
        :http-request="handleFolderUpload"
        :show-file-list="false"
        multiple
      >
        <!-- <ElButton size="small" style="margin-left: 10px" @click="enableFolderUpload">
          上传文件夹
        </ElButton> -->
      </ElUpload>
      <ElButton v-if="uploadingFiles.length > 0" size="small" style="margin-left: 10px" @click="showUploadProgress = !showUploadProgress">
        {{ showUploadProgress ? '隐藏进度' : '显示进度' }}
      </ElButton>
    </div>

    <!-- 上传进度显示 -->
    <div v-if="showUploadProgress && uploadingFiles.length > 0" class="upload-progress-container">
      <div v-for="file in uploadingFiles" :key="file.id" class="upload-progress-item">
        <div class="upload-file-info">
          <div class="upload-file-name" :title="file.name">{{ file.name }}</div>
          <div class="upload-file-status">
            {{ file.status === 'uploading' ? `${file.progress}%` :
               file.status === 'success' ? '上传成功' : '上传失败' }}
          </div>
        </div>
        <ElProgress
          :percentage="file.progress"
          :status="file.status === 'uploading' ? '' : file.status === 'success' ? 'success' : 'exception'"
        />
      </div>
    </div>

    <ElTable
      :data="fileList"
      style="width: 100%"
      v-loading="loading"
      @row-dblclick="handleRowDblClick"
      @row-contextmenu="handleRowContextMenu"
    >
      <ElTableColumn label="名称" min-width="200">
        <template #default="{row}">
          <div class="file-item">
            <ElIcon>
              <component :is="row.type === 'folder' ? ElementPlusIcons.SvgFolder : ElementPlusIcons.SvgDocument" />
            </ElIcon>
            <ElLink>{{ row.name }}</ElLink>
          </div>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="size" label="大小" width="120">
        <template #default="{row}">
          {{ formatFileSize(row.size) }}
        </template>
      </ElTableColumn>
      <ElTableColumn prop="updateTime" label="修改时间" width="180">
        <template #default="{row}">
          {{ formatDate(row.updateTime) }}
        </template>
      </ElTableColumn>
      <ElTableColumn label="操作" width="150" align="center">
        <template #default="{row}">
          <ElDropdown trigger="click">
            <ElButton size="small">
              <ElIcon><ElementPlusIcons.SvgMore /></ElIcon>
            </ElButton>
            <template #dropdown>
              <ElDropdownMenu>
                <ElDropdownItem v-if="row.type !== 'folder'" @click="downloadFile(row)">
                  下载
                </ElDropdownItem>
                <!-- 只对文件显示重命名选项 -->
                <ElDropdownItem v-if="row.type !== 'folder'" @click="renameFile(row)">
                  重命名
                </ElDropdownItem>
                <ElDropdownItem @click="deleteFile(row)" divided>
                  <span class="text-red-500">删除</span>
                </ElDropdownItem>
              </ElDropdownMenu>
            </template>
          </ElDropdown>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 右键菜单 -->
    <Teleport to="body">
      <div
        v-show="contextMenuVisible"
        class="context-menu"
        :style="{top: contextMenuTop + 'px', left: contextMenuLeft + 'px'}"
      >
        <div v-if="contextMenuRow && contextMenuRow.type !== 'folder'" class="context-menu-item" @click="downloadFile(contextMenuRow)">下载</div>
        <!-- 只对文件显示重命名选项 -->
        <div v-if="contextMenuRow && contextMenuRow.type !== 'folder'" class="context-menu-item" @click="renameFile(contextMenuRow)">重命名</div>
        <div class="context-menu-item delete" @click="deleteFile(contextMenuRow)">删除</div>
      </div>
    </Teleport>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { requestClient } from '#/api/request';
import { useMessage } from '#/components/elementPlus/useMessage';
import dayjs from 'dayjs';
import {
  ElButton,
  ElLink,
  ElUpload,
  ElTable,
  ElTableColumn,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElIcon,
  ElMessageBox,
  ElTree,
  ElProgress
} from 'element-plus';

import * as   ElementPlusIcons from '@vben/icons'

import {  } from 'element-plus';

// 格式化日期
function formatDate(date: string | number | Date): string {
  if (!date) return '';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
}

// 格式化文件大小
function formatFileSize(size: number): string {
  if (!size) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let index = 0;
  let fileSize = size;

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }

  return `${fileSize.toFixed(2)} ${units[index]}`;
}

defineOptions({
  name: 'FileExplorer'
});

defineProps({
  title: {
    type: String,
    default: '文件管理器'
  }
});

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  onOpenChange(isOpen) {
    if (isOpen) {

    }
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    //获取到当前选择的数据
  }
});
const { showMessage } = useMessage();

const loading = ref(false);
const currentPath = ref('/');
const fileList = ref<Array<{
  name: string;
  type: string;
  size: number;
  updateTime: string;
}>>([]);
const fileUploadRef = ref();
const folderUploadRef = ref();

// 上传相关变量
interface UploadingFile {
  id: string;
  name: string;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  size: number;
}

const uploadingFiles = ref<UploadingFile[]>([]);
const showUploadProgress = ref(false);

// 右键菜单相关变量
const contextMenuVisible = ref(false);
const contextMenuTop = ref(0);
const contextMenuLeft = ref(0);
const contextMenuRow = ref<any>(null);

// 目录树相关变量
const directoryTreeRef = ref();
const directoryTree = ref<Array<{
  name: string;
  path: string;
  type: string;
  children?: any[];
}>>([]);

const pathSegments = computed(() => {
  return currentPath.value.split('/')
    .filter(segment => segment)
    .map((segment, index, arr) => ({
      name: segment,
      path: '/' + arr.slice(0, index + 1).join('/')
    }));
});

onMounted(() => {
  refreshList();
  refreshDirectoryTree();

  // 添加点击外部关闭右键菜单的事件监听
  document.addEventListener('click', closeContextMenu);
  document.addEventListener('contextmenu', closeContextMenu);
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  document.removeEventListener('click', closeContextMenu);
  document.removeEventListener('contextmenu', closeContextMenu);
});

// 处理行右键点击事件
function handleRowContextMenu(row: any, _column: any, event: MouseEvent) {
  // 阻止默认右键菜单
  event.preventDefault();
  event.stopPropagation();

  // 设置菜单位置，使用 pageX/pageY 而不是 clientX/clientY
  contextMenuLeft.value = event.pageX;
  contextMenuTop.value = event.pageY;

  // 确保菜单不会超出视口边界
  nextTick(() => {
    const menu = document.querySelector('.context-menu') as HTMLElement;
    if (menu) {
      const menuRect = menu.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // 检查右边界
      if (event.clientX + menuRect.width > viewportWidth) {
        contextMenuLeft.value = event.pageX - menuRect.width;
      }

      // 检查下边界
      if (event.clientY + menuRect.height > viewportHeight) {
        contextMenuTop.value = event.pageY - menuRect.height;
      }
    }
  });

  contextMenuRow.value = row;
  contextMenuVisible.value = true;
}

// 关闭右键菜单
function closeContextMenu() {
  contextMenuVisible.value = false;
};

// 在 script setup 中添加以下模拟数据结构
// 模拟文件系统数据，包含所有文件和文件夹的层级结构
interface FileItem {
  name: string;
  type: string;
  size: number;
  updateTime: string;
}

type FileSystemType = Record<string, FileItem[]>;

const mockFileSystem: FileSystemType = {
  '/': [
    {
      name: '文档资料',
      type: 'folder',
      size: 0,
      updateTime: '2024-01-15 10:30:00'
    },
    {
      name: '源代码',
      type: 'folder',
      size: 0,
      updateTime: '2024-01-13 09:15:00'
    },
    {
      name: '会议记录.txt',
      type: 'file',
      size: 1024 * 25, // 25KB
      updateTime: '2024-01-11 11:00:00'
    },
    {
      name: '项目简介.pdf',
      type: 'file',
      size: 1024 * 1024 * 1.2, // 1.2MB
      updateTime: '2024-01-10 14:20:00'
    }
  ],
  '/文档资料': [
    {
      name: '项目文档',
      type: 'folder',
      size: 0,
      updateTime: '2024-01-14 16:30:00'
    },
    {
      name: '技术规范',
      type: 'folder',
      size: 0,
      updateTime: '2024-01-13 11:45:00'
    },
    {
      name: '需求分析.docx',
      type: 'file',
      size: 1024 * 1024 * 1.8, // 1.8MB
      updateTime: '2024-01-12 09:30:00'
    }
  ],
  '/文档资料/项目文档': [
    {
      name: '项目计划.docx',
      type: 'file',
      size: 1024 * 1024 * 2.5, // 2.5MB
      updateTime: '2024-01-14 15:20:00'
    },
    {
      name: '进度报告.xlsx',
      type: 'file',
      size: 1024 * 512, // 512KB
      updateTime: '2024-01-13 17:40:00'
    },
    {
      name: '风险评估.docx',
      type: 'file',
      size: 1024 * 1024 * 1.3, // 1.3MB
      updateTime: '2024-01-12 14:15:00'
    }
  ],
  '/文档资料/技术规范': [
    {
      name: '前端规范.md',
      type: 'file',
      size: 1024 * 120, // 120KB
      updateTime: '2024-01-11 10:25:00'
    },
    {
      name: '后端规范.md',
      type: 'file',
      size: 1024 * 150, // 150KB
      updateTime: '2024-01-10 16:35:00'
    },
    {
      name: '数据库设计.pdf',
      type: 'file',
      size: 1024 * 1024 * 3.2, // 3.2MB
      updateTime: '2024-01-09 11:20:00'
    }
  ],
  '/源代码': [
    {
      name: '前端',
      type: 'folder',
      size: 0,
      updateTime: '2024-01-12 13:40:00'
    },
    {
      name: '后端',
      type: 'folder',
      size: 0,
      updateTime: '2024-01-11 15:30:00'
    },
    {
      name: '测试报告.pdf',
      type: 'file',
      size: 1024 * 1024 * 5.8, // 5.8MB
      updateTime: '2024-01-12 16:45:00'
    },
    {
      name: 'README.md',
      type: 'file',
      size: 1024 * 15, // 15KB
      updateTime: '2024-01-10 09:15:00'
    }
  ],
  '/源代码/前端': [
    {
      name: 'src',
      type: 'folder',
      size: 0,
      updateTime: '2024-01-09 14:20:00'
    },
    {
      name: 'package.json',
      type: 'file',
      size: 1024 * 5, // 5KB
      updateTime: '2024-01-08 11:30:00'
    },
    {
      name: 'tsconfig.json',
      type: 'file',
      size: 1024 * 3, // 3KB
      updateTime: '2024-01-07 16:45:00'
    }
  ],
  '/源代码/前端/src': [
    {
      name: 'components',
      type: 'folder',
      size: 0,
      updateTime: '2024-01-06 10:15:00'
    },
    {
      name: 'App.vue',
      type: 'file',
      size: 1024 * 2, // 2KB
      updateTime: '2024-01-05 15:30:00'
    },
    {
      name: 'main.ts',
      type: 'file',
      size: 1024 * 1, // 1KB
      updateTime: '2024-01-04 09:45:00'
    }
  ],
  '/源代码/后端': [
    {
      name: 'controllers',
      type: 'folder',
      size: 0,
      updateTime: '2024-01-03 14:20:00'
    },
    {
      name: 'models',
      type: 'folder',
      size: 0,
      updateTime: '2024-01-02 11:35:00'
    },
    {
      name: 'server.js',
      type: 'file',
      size: 1024 * 8, // 8KB
      updateTime: '2024-01-01 16:50:00'
    }
  ]
};

// 初始文件列表变量已在上面声明，这里不需要再初始化

// 模拟目录树数据
const mockDirectoryTree = [
  {
    name: '根目录',
    path: '/',
    type: 'folder',
    children: [
      {
        name: '文档资料',
        path: '/文档资料',
        type: 'folder',
        children: [
          {
            name: '项目文档',
            path: '/文档资料/项目文档',
            type: 'folder',
            children: [
              {
                name: '项目计划.docx',
                path: '/文档资料/项目文档/项目计划.docx',
                type: 'file'
              },
              {
                name: '进度报告.xlsx',
                path: '/文档资料/项目文档/进度报告.xlsx',
                type: 'file'
              },
              {
                name: '风险评估.docx',
                path: '/文档资料/项目文档/风险评估.docx',
                type: 'file'
              }
            ]
          },
          {
            name: '技术规范',
            path: '/文档资料/技术规范',
            type: 'folder',
            children: [
              {
                name: '前端规范.md',
                path: '/文档资料/技术规范/前端规范.md',
                type: 'file'
              },
              {
                name: '后端规范.md',
                path: '/文档资料/技术规范/后端规范.md',
                type: 'file'
              },
              {
                name: '数据库设计.pdf',
                path: '/文档资料/技术规范/数据库设计.pdf',
                type: 'file'
              }
            ]
          },
          {
            name: '需求分析.docx',
            path: '/文档资料/需求分析.docx',
            type: 'file'
          }
        ]
      },
      {
        name: '源代码',
        path: '/源代码',
        type: 'folder',
        children: [
          {
            name: '前端',
            path: '/源代码/前端',
            type: 'folder',
            children: [
              {
                name: 'src',
                path: '/源代码/前端/src',
                type: 'folder',
                children: [
                  {
                    name: 'components',
                    path: '/源代码/前端/src/components',
                    type: 'folder'
                  },
                  {
                    name: 'App.vue',
                    path: '/源代码/前端/src/App.vue',
                    type: 'file'
                  },
                  {
                    name: 'main.ts',
                    path: '/源代码/前端/src/main.ts',
                    type: 'file'
                  }
                ]
              },
              {
                name: 'package.json',
                path: '/源代码/前端/package.json',
                type: 'file'
              },
              {
                name: 'tsconfig.json',
                path: '/源代码/前端/tsconfig.json',
                type: 'file'
              }
            ]
          },
          {
            name: '后端',
            path: '/源代码/后端',
            type: 'folder',
            children: [
              {
                name: 'controllers',
                path: '/源代码/后端/controllers',
                type: 'folder'
              },
              {
                name: 'models',
                path: '/源代码/后端/models',
                type: 'folder'
              },
              {
                name: 'server.js',
                path: '/源代码/后端/server.js',
                type: 'file'
              }
            ]
          },
          {
            name: '测试报告.pdf',
            path: '/源代码/测试报告.pdf',
            type: 'file'
          },
          {
            name: 'README.md',
            path: '/源代码/README.md',
            type: 'file'
          }
        ]
      },
      {
        name: '会议记录.txt',
        path: '/会议记录.txt',
        type: 'file'
      },
      {
        name: '项目简介.pdf',
        path: '/项目简介.pdf',
        type: 'file'
      }
    ]
  }
];

// 修改 refreshList 函数，根据当前路径获取对应的文件列表
async function refreshList() {
  loading.value = true;
  try {
    // 模拟异步请求延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    // 根据当前路径获取对应的文件列表
    if (mockFileSystem[currentPath.value]) {
      fileList.value = mockFileSystem[currentPath.value] as Array<{
        name: string;
        type: string;
        size: number;
        updateTime: string;
      }>;
    } else {
      // 如果路径不存在，显示空列表
      fileList.value = [];
      showMessage('warning', '该目录不存在或为空');
    }
  } catch (error) {
    showMessage('error', '获取文件列表失败');
  } finally {
    loading.value = false;
  }
}

function navigateBack() {
  const segments = currentPath.value.split('/').filter(s => s);
  segments.pop();
  currentPath.value = segments.length ? '/' + segments.join('/') : '/';
  refreshList();
}

function navigateToRoot() {
  currentPath.value = '/';
  refreshList();
}

function navigateToPath(path: string) {
  currentPath.value = path;
  refreshList();
}

async function createFolder() {
  try {
    const result = await ElMessageBox.prompt('请输入文件夹名称', '新建文件夹', {
      inputPattern: /^[^/:*?"<>|]+$/,
      inputErrorMessage: '文件夹名称不能包含特殊字符'
    });

    if (result.value) {
      await requestClient.post('/file/createFolder', {
        path: currentPath.value,
        name: result.value
      });
      showMessage('success', '创建成功');
      refreshList();
    }
  } catch (error) {
    if (error !== 'cancel') {
      showMessage('error', '创建文件夹失败');
    }
  }
}

async function handleFileUpload({ file }: { file: File }) {
  try {
    // 生成唯一ID
    const fileId = Date.now() + '-' + Math.random().toString(36).substring(2, 11);

    // 添加到上传列表
    const uploadingFile: UploadingFile = {
      id: fileId,
      name: file.name,
      progress: 0,
      status: 'uploading',
      size: file.size
    };

    uploadingFiles.value.push(uploadingFile);
    showUploadProgress.value = true;

    // 计算分片大小和数量
    const chunkSize = 1024 * 1024 * 2; // 2MB 每片
    const chunks = Math.ceil(file.size / chunkSize);

    // 模拟分片上传过程
    for (let i = 0; i < chunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(file.size, start + chunkSize);
      const chunk = file.slice(start, end);

      // 创建分片的FormData
      const formData = new FormData();
      formData.append('file', chunk);
      formData.append('path', currentPath.value);
      formData.append('fileName', file.name);
      formData.append('chunkIndex', i.toString());
      formData.append('chunks', chunks.toString());

      try {
        // 这里使用模拟的上传，实际项目中应该调用真实的分片上传API
        // await requestClient.upload('/file/uploadChunk', {
        //   file: chunk,
        //   path: currentPath.value,
        //   fileName: file.name,
        //   chunkIndex: i,
        //   chunks
        // });

        // 模拟上传延迟
        await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 500));

        // 更新进度
        const index = uploadingFiles.value.findIndex(item => item.id === fileId);
        if (index !== -1 && uploadingFiles.value[index]) {
          uploadingFiles.value[index].progress = Math.round(((i + 1) / chunks) * 100);

          // 如果是最后一片，标记为成功
          if (i === chunks - 1) {
            uploadingFiles.value[index].status = 'success';

            // 5秒后自动从列表中移除
            setTimeout(() => {
              const removeIndex = uploadingFiles.value.findIndex(item => item.id === fileId);
              if (removeIndex !== -1) {
                uploadingFiles.value.splice(removeIndex, 1);

                // 如果没有正在上传的文件，隐藏进度条
                if (uploadingFiles.value.length === 0) {
                  showUploadProgress.value = false;
                }
              }
            }, 5000);
          }
        }
      } catch (err) {
        // 更新状态为错误
        const index = uploadingFiles.value.findIndex(item => item.id === fileId);
        if (index !== -1 && uploadingFiles.value[index]) {
          uploadingFiles.value[index].status = 'error';
        }
        throw err;
      }
    }

    // 所有分片上传完成后，通知服务器合并分片
    // 实际项目中应该调用合并API
    // await requestClient.post('/file/mergeChunks', {
    //   fileName: file.name,
    //   path: currentPath.value,
    //   chunks
    // });

    showMessage('success', '上传成功');
    refreshList();
  } catch (error) {
    showMessage('error', '上传失败');
  }
}

// 暂时不使用文件夹上传功能
// function enableFolderUpload() {
//   nextTick(() => {
//     const input = folderUploadRef.value.$el.querySelector('input');
//     input.webkitdirectory = true;
//   });
// }

async function handleFolderUpload({ file }: { file: File & { webkitRelativePath: string } }) {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('path', currentPath.value);
    formData.append('relativePath', file.webkitRelativePath);

    await requestClient.upload('/file/upload', {
      file,
      path: currentPath.value,
      relativePath: file.webkitRelativePath
    });
    showMessage('success', '上传成功');
    refreshList();
  } catch (error) {
    showMessage('error', '上传失败');
  }
}

async function deleteFile(file: { name: string; type?: string }) {
  try {
    // 判断是否是文件夹
    const isFolder = file.type === 'folder';
    const confirmTitle = isFolder ? '确定要删除该文件夹吗？' : '确定要删除该文件吗？';
    const confirmMsg = isFolder ? '删除文件夹将会删除其中的所有文件和子文件夹，此操作不可恢复。' : '此操作不可恢复。';

    await ElMessageBox.confirm(confirmMsg, confirmTitle, {
      type: 'warning',
      confirmButtonText: '确定删除',
      cancelButtonText: '取消'
    });

    // 调用删除 API，传递文件类型信息
    await requestClient.post('/file/delete', {
      path: currentPath.value,
      name: file.name,
      type: file.type || 'file' // 默认为文件类型
    });

    showMessage('success', '删除成功');

    // 刷新文件列表和目录树
    refreshList();
    refreshDirectoryTree();
  } catch (error) {
    if (error !== 'cancel') {
      showMessage('error', '删除失败');
    }
  }
}

async function renameFile(file: { name: string; type?: string }) {
  try {
    // 如果是文件夹，则不允许重命名
    if (file.type === 'folder') {
      showMessage('warning', '文件夹不能重命名');
      return;
    }

    // 文件重命名提示
    const title = '重命名文件';
    const placeholder = '请输入新的文件名称';
    const errorMsg = '文件名称不能包含特殊字符';

    const result = await ElMessageBox.prompt(placeholder, title, {
      inputValue: file.name,
      inputPattern: /^[^/:*?"<>|]+$/,
      inputErrorMessage: errorMsg
    });

    if (result.value) {
      // 调用重命名 API
      await requestClient.post('/file/rename', {
        path: currentPath.value,
        oldName: file.name,
        newName: result.value,
        type: 'file' // 只允许文件重命名
      });

      showMessage('success', '重命名成功');

      // 刷新文件列表和目录树
      refreshList();
      refreshDirectoryTree();
    }
  } catch (error) {
    if (error !== 'cancel') {
      showMessage('error', '重命名失败');
    }
  }
}

async function downloadFile(file: { name: string; type?: string }) {
  try {
    // 只允许下载文件，不允许下载文件夹
    if (file.type === 'folder') {
      showMessage('warning', '文件夹不能下载');
      return;
    }

    // 下载文件
    const response = await requestClient.download(`/file/download?path=${encodeURIComponent(currentPath.value)}&name=${encodeURIComponent(file.name)}`);
    const url = window.URL.createObjectURL(response);

    // 创建下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    showMessage('success', '文件下载成功');
  } catch (error) {
    showMessage('error', '下载失败');
  }
}

function handleRowDblClick(row: { type: string; name: string }) {
  if (row.type === 'folder') {
    currentPath.value = currentPath.value === '/'
      ? `/${row.name}`
      : `${currentPath.value}/${row.name}`;
    refreshList();
  } else {
    // 预览文件
    previewFile(row);
  }
}

async function previewFile(file: { name: string }) {
  try {
    const response = await requestClient.post('/file/getPreviewUrl', {
      path: currentPath.value,
      name: file.name
    });
    window.open(response.data.url);
  } catch (error) {
    showMessage('error', '预览失败');
  }
}

// 刷新目录树
async function refreshDirectoryTree() {
  try {
    // 模拟异步请求延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    // 使用模拟数据
    directoryTree.value = mockDirectoryTree;
  } catch (error) {
    showMessage('error', '获取目录树失败');
  }
}

// 处理目录树节点点击事件
function handleNodeClick(data: any) {
  if (data.type === 'folder') {
    // 如果是文件夹，则导航到该目录
    currentPath.value = data.path;
    refreshList();
  } else if (data.type === 'file') {
    // 如果是文件，则预览文件
    const fileName = data.name || data.path.split('/').pop();
    previewFile({ name: fileName });
  }
}
defineExpose(modalApi);
</script>

<style scoped>
/* 文件浏览器容器样式 */
.file-explorer-container {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* 目录树样式 */
.directory-tree {
  width: 250px;
  border-right: 1px solid #e4e7ed;
  padding: 10px;
  overflow: auto;
  height: 100%;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.tree-title {
  font-weight: bold;
  font-size: 14px;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 文件内容区域样式 */
.file-content {
  flex: 1;
  padding: 0 10px;
  overflow: auto;
  height: 100%;
}

.path-navigator {
  display: flex;
  align-items: center;
  background: #f5f7fa;
  padding: 0 10px;
  border-radius: 4px;
  flex: 1;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed; /* 使用 fixed 定位，因为我们使用了 Teleport to="body" */
  z-index: 9999;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 5px 0;
  min-width: 120px;
  max-width: 200px; /* 限制最大宽度 */
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
}

.context-menu-item.delete {
  color: #f56c6c;
}

.context-menu-item.delete:hover {
  background-color: #fef0f0;
}

/* 上传进度相关样式 */
.upload-progress-container {
  margin-bottom: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
}

.upload-progress-item {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #e4e7ed;
}

.upload-progress-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.upload-file-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.upload-file-name {
  font-size: 14px;
  color: #303133;
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.upload-file-status {
  font-size: 12px;
  color: #909399;
}
</style>





