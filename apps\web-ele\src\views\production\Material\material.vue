<script lang="ts" setup>
import { ref, onMounted } from 'vue';

import { $t } from '#/locales';

import {  ElMessage } from "element-plus";
import { useVbenForm } from '#/adapter/form';
import { PageCard } from '@vben/common-ui';
// import { IconifyIcon } from '@vben/icons';
import { getMaterialList, deleteTCMInfo, releaseMaterial, cancelReleaseMaterial } from '#/api/production/material'
import { TableAction } from '#/components/table-action';

import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';

import materialCreateOrEdit from './materialCreateOrEdit.vue';
import { useMessage } from '#/components/elementPlus/useMessage';

import fileExplorer from '#/views/common/FileExplorer.vue';

const materialCreateOrEditRef = ref();
const fileExplorerRef = ref();
async function getData(data: any | [] = []) {
  const res = await getMaterialList({
    ...data
  });
  rowData.value = res;
}

const gridRef = ref();


const columnDefs: any[] = [
  // { headerName: $t('production.Seq'), field: 'seq' },
  { headerName: $t('production.RawMaterialCode'), field: 'mCode', width: 135 },
  { headerName: $t('production.RawMaterialName'), field: 'mName', width: 140 },
  {
    headerName: $t('production.NowStatus'),
    field: 'status',
    cellRenderer: (params: any) => {
      if (params.value === 'new') {
        return $t('production.New');
      }
      if (params.value === 'release') {
        return $t('production.Release');
      }
      if (params.value === 'cancel') {
        return $t('production.CancelRelease');
      }
      return params.value;
    },
    width: 135
  },
  { headerName: $t('production.ProdCode'), field: 'productCode', width: 135 },
  { headerName: $t('production.ProdName'), field: 'productName', width: 135 },
  { headerName: $t('production.Unit'), field: 'unit', width: 125 },
  { headerName: $t('production.SpecModel'), field: 'specModel', width: 135 },
  { headerName: $t('production.InternalNo'), field: 'intCode', width: 135 },
  { headerName: $t('production.MatItemNo'), field: 'matItemNo', width: 135 },
  { headerName: $t('production.OriginCountry'), field: 'originCountry', width: 125 },
  { headerName: $t('production.UnitPrice'), field: 'unitPrice', width: 115 },
  { headerName: $t('production.Width'), field: 'width', width: 115 },
  { headerName: $t('production.GSM'), field: 'gsm', width: 165 },
  { headerName: $t('production.HSUpdater'), field: 'hsUpdater', width: 135 },
  { headerName: $t('production.HSTimeUpdated'), field: 'hsTimeUpdated', width: 135 },
  { headerName: $t('production.HSReviewer'), field: 'reviewer', width: 135 },
  { headerName: $t('production.HSTimeReviewed'), field: 'timeReviewed', width: 135 },
  { headerName: $t('production.Obsolete'), field: 'obsolete', width: 125 },
  {
    headerName: $t('production.Action'),
    field: "action", // 注意这里field不是实际的数据字段，仅用于标识
    pinned: 'right',
    cellRenderer: 'actionCell',
    flex: 1,
    cellRendererParams: {
      actions: [
        {
          label: $t('production.Edit'),
          callback: (data: any) => {
            handleEdit(data,"edit")
          },
          auth: ['production.material.edit'],
          type: 'primary',
          size: 'small',
          disabled: (params: any) => params.data.status !== 'new' && params.data.status !== 'cancel', // Disable if status is not 'new' or 'cancel'
          //eventName: 'editEvent' // 自定义事件名
        },
        {
          label: $t('basic.view'),
          callback: (data: any) => {
            handleEdit(data,"view")
          },
          auth: ['production.material.view'],
          type: 'primary',
          size: 'small',
        },
        {
          label: $t('production.Delete'),
          callback: (data: any) => {
            handleDelete(data)
          },
          auth: ['production.material.delete'],
          type: 'danger',
          size: 'small',
          disabled: (params: any) => params.data.status !== 'new' && params.data.status !== 'cancel', // Disable if status is not 'new' or 'cancel'
        },
      ]
    }
  },

];
const defaultColDef: any = {
  //flex: 1

};
const rowData = ref<any[]>([])


const [QueryForm] = useVbenForm({
  handleSubmit: (values) => {
    getData(values);
  },
  submitButtonOptions: {
    content: $t('page.base.search'),
  },
  resetButtonOptions: {
    content: $t('page.base.reset'),
  },
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  collapsed: true,
  showCollapseButton: true,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  schema: [
    {
      label: $t('production.RawMaterialCode'),
      component: 'Input',
      fieldName: 'mCode',
    },
    {
      label: $t('production.RawMaterialName'),
      component: 'Input',
      fieldName: 'mName',
    }
  ]
});

const handleAdd = (data: any) => {
  //获取到勾选数据

  materialCreateOrEditRef.value.setData({
    record: data,
    isUpdate: false,
    isView: false,
  });
  materialCreateOrEditRef.value.open();
  materialCreateOrEditRef.value.onClosed = () => {
    //调用刷新数据的方法
    getData();
  };

}

const handleEdit = (data: any,pageType:String) => {

  materialCreateOrEditRef.value.setData({
    record: data,
    pageType: pageType,
  });
  materialCreateOrEditRef.value.open();
  materialCreateOrEditRef.value.onClosed = () => {
    // 调用刷新数据的方法
    getData();
  };

}

const handleRelease = async () => {
  //获取到勾选数据
  const gridRows = gridRef.value?.gridApi?.getSelectedRows();
  //如果没有勾选数据，则提示
  if (!gridRows || gridRows.length === 0) {
    useMessage().showMessage('warning', $t('basic.PleaseSelectData'));
    return;
  }
  //进行二次确认
  useMessage().showMessageBox('confirm', $t('production.ConfirmRelease'), $t('basic.tips'), 'warning', async (action) => {
    if (action === 'confirm') {
      await releaseMaterial({ id: gridRows[0].id });
      getData();
      useMessage().showMessage('success', $t('production.ReleaseSuccess'));
    }
  });
}

const handleCancel = async () => {
  //获取到勾选数据
  const gridRows = gridRef.value?.gridApi?.getSelectedRows();
  //如果没有勾选数据，则提示
  if (!gridRows || gridRows.length === 0) {
    useMessage().showMessage('warning', $t('basic.PleaseSelectData'));
    return;
  }
  //进行二次确认
  useMessage().showMessageBox('confirm', $t('production.ConfirmCancelRelease'), $t('basic.tips'), 'warning', async (action) => {
    if (action === 'confirm') {
      await cancelReleaseMaterial({ id: gridRows[0].id });
      getData();
      useMessage().showMessage('success', $t('production.CancelReleaseSuccess'));
    }
  });
}

const handleFile = async() =>{
  fileExplorerRef.value.open();

}

const rowSelection = ref({
  mode: "singleRow" as "singleRow",  // singleRow 默认单选//multiRow
  checkboxes: true,//默认关闭
  headerCheckbox: true, //默认关闭
  copySelectedRows: true,
});

//删除
const handleDelete = async (data: any) => {
  try {
    await deleteTCMInfo({ id: data.id }); // 假设 data.id 是要删除的记录的 ID
    // 这里可以添加一个提示框，使用 Element Plus 的 Message 组件
    ElMessage.success($t('production.DeleteSuccess')); // 提示删除成功
    getData(); // 重新获取数据以更新表格
  } catch (error) {
    ElMessage.error($t('production.DeleteFailed')); // 提示删除失败
  }
}

onMounted(() => {
  getData();
});
</script>

<template>
  <PageCard auto-content-height>

    <QueryForm />

    <TableAction :actions="[
      {
        label: $t('production.Add'),
        type: 'primary',
        icon: 'ep:plus',
        auth: ['production.material.add'],
        onClick: handleAdd.bind(null),
      },
      {
        label: $t('production.Release'),
        type: 'primary',
        icon: 'ic:twotone-rocket-launch',
        auth: ['production.material.release'],
        onClick: handleRelease.bind(null),
      },
      {
        label: $t('production.CancelRelease'),
        type: 'primary',
        icon: 'ic:baseline-cancel',
        auth: ['production.material.cancel'],
        onClick: handleCancel.bind(null),
      },
       {
        label: '文件',
        type: 'primary',
        icon: 'ep:plus',
        auth: ['production.material.add'],
        onClick: handleFile.bind(null),
      },
    ]">
    </TableAction>

      <ClientGridComponent ref="gridRef" :columnDefs="columnDefs" :rowData="rowData" :pageSize="20"
      :defaultColDef="defaultColDef" :rowSelection="rowSelection"  />
    

    <materialCreateOrEdit ref="materialCreateOrEditRef" />

    <fileExplorer ref="fileExplorerRef" />



  </PageCard>

</template>
