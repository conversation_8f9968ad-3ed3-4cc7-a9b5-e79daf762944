<template>
  <div>
    <ElButton
      v-for="(action, index) in actionList"
      :key="index"
      :type="action.type as 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text' || 'default'"
      :size="getButtonSize(action.size)"
      @click="handleAction(action.callback, action.eventName)"
      :class="action.class"
    >
      {{ action.label }}
    </ElButton>
  </div>
</template>

<script lang="ts" setup>
import { ElButton } from 'element-plus';
import { defineProps, defineEmits } from 'vue';
import { type ICellRendererParams } from 'ag-grid-community';

interface Action {
  label: string;
  callback: Function;
  type?: string;
  size?: 'small' | 'default' | 'large'; // 明确 size 的类型
  class?: string;
  eventName?: string; // 自定义事件名称
}

interface ActionCellProps {
  params: ICellRendererParams & {
    actions?: Action[];
  };
}

const props = defineProps<{
  params: ActionCellProps['params'];
}>();

// 获取按钮大小，默认为 'small'
function getButtonSize(size?: 'small' | 'default' | 'large'): 'small' | 'default' | 'large' {
  return size || 'small';
}
// 定义 emit 事件
const emit = defineEmits<{
  (event: 'actionTriggered', eventName: string, data: any): void;
}>();

// 通过 props 中的 actions 配置按钮
const actionList = props.params.actions || [];

// 处理按钮点击
function handleAction(callback: Function, eventName?: string) {
  if (callback) {
    callback(props.params.data); // 执行回调
  }
  if (eventName) {
    emit('actionTriggered', eventName, props.params.data); // 触发自定义事件
  }
}
</script>

<style scoped>
div {
  display: flex;
  gap: 10px;
}
</style>
