import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ic:baseline-view-in-ar',
      keepAlive: true,
      order: 200,
      title: $t('demos.title'),
      hideInMenu: true,
    },
    name: 'De<PERSON>',
    path: '/demos',
    children: [
      {
        meta: {
          title: $t('demos.elementPlus'),
          // perms: ['NaiveDemos21231'],
          // authority: ['admin'],
        },
        name: 'NaiveDemos',
        path: '/demos/element',
        component: () => import('#/views/demos/element/index.vue'),
      },
      {
        meta: {
          title: $t('demos.form'),
        },
        name: 'BasicForm',
        path: '/demos/form',
        component: () => import('#/views/demos/form/basic.vue'),
      },
    ],
  },
];

export default routes;
