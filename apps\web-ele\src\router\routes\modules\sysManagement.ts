import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-view-in-ar',
      keepAlive: true,
      order: 100,
      title: $t('sysManagement.sysManagement'),
      perms: ['sysManagement'],
    },
    name: 'SysManagement',
    path: '/sysManagement',
    children: [
      {
        meta: {
          title: $t('sysManagement.userManagement'),
          // 保存当前页面状态
          keepAlive: true,
          perms: ['user.home'],
          buttons: [
            {
              name: "user.add",
              meta:{
                icon: "ic:baseline-add",
                title:"新增",
                perms: ['user.add'],
              }
              //action: 'addRole',
            },
            {
              name: "user.edit",
              meta:{
                icon: "ic:baseline-edit",
                title:"编辑",
                perms: ['user.edit'],
              }
              //action: 'editRole',
            },
            {
              name: "user.delete",
              meta:{
                icon: "ic:round-delete",
                title:"禁用",
                perms: ['user.delete'],
              }
              //action: 'deleteRole',
            },
            {
              name: "user.role",
              meta:{
                icon: "ic:baseline-account-circle",
                title:"授权角色",
                perms: ['user.role'],
              }
              //action: 'deleteRole',
            },
          ],
          order: 100,
        },
        name: 'UserManagement',
        path: '/sysManagement/userManagement',
        component: () =>
          import('#/views/sysManagement/userManagement/userManagement.vue'),
        
      },
      {
        meta: {
          title: $t('sysManagement.roleManagement'),
          // 保存当前页面状态
          keepAlive: true,
          perms: ['role'],
          order: 200,
          buttons: [
            {
              name: "role:add",
              meta: {
                icon: "ic:baseline-add",
                title: $t('production.Add'),
                perms: ['role.add'],
              }
            }
          ],
        },
        name: 'RoleManagement',
        path: '/sysManagement/role',
        component: () =>
          import('#/views/sysManagement/role/role.vue'),
      },
      {
        meta: {
          title: $t('sysManagement.organization'),
          // 保存当前页面状态
          keepAlive: true,
          perms: ['organization'],
          buttons: [
            {
              name: "organization:add",
              meta: {
                icon: "ic:baseline-add",
                title: $t('production.Add'),
                perms: ['organization.add'],
              }
            }
          ],
          order: 200,
        },
        name: 'Organization',
        path: '/Organization/Organization',
        component: () =>
          import('#/views/sysManagement/Organization/Organization.vue'),
      }
    ],
  },
];

export default routes;
