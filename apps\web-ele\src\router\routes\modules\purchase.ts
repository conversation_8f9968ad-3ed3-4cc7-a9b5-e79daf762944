import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:clipboard-check-multiple-outline',
      keepAlive: true,
      order: 1000,
      title: $t('purchase.purchase'),
      perms: ['purchase'],
    },
    name: 'Purchase',
    path: '/Purchase',
    children: [
      {

        meta: {
          title: $t('purchase.imported'),
          icon: 'ic:outline-view-compact',
          // 保存当前页面状态
          keepAlive: true,
          perms: ['purchase.imported'],
          buttons: [
            {
              name: "purchase.imported.add",
              meta: {
                icon: "ic:baseline-add",
                title: $t('basic.import'),
                perms: ['purchase.imported.add'],
              }
            },
            {
              name: "purchase.imported.view",
              meta: {
                icon: "ic:baseline-remove-red-eye",
                title: $t('basic.view'),
                perms: ['purchase.imported.view'],
              }
            },
            {
              name: "purchase.imported.confirm",
              meta: {
                icon: "ic:baseline-check-circle",
                title: $t('purchase.confirmOrder'),
                perms: ['purchase.imported.confirm'],
              }
            },
            {
              name: "purchase.imported.cancelConfirm",
              meta: {
                icon: "ic:baseline-cancel",
                title: $t('purchase.cancelConfirm'),
                perms: ['purchase.imported.cancelConfirm'],
              }
            },
            {
              name: "purchase.imported.delete",
              meta: {
                icon: "ic:round-delete",
                title: $t('basic.delete'),
                perms: ['purchase.imported.delete'],
              }
            },
          ],

          order: 100,
        },
        name: 'purchase',
        path: '/Purchase/index',
        component: () => import('#/views/purchase/imported/index.vue'),


      }
      ,{

        meta: {
          title: $t('purchase.local'),
          icon: 'ic:outline-view-compact',
          // 保存当前页面状态
          keepAlive: true,
          perms: ['purchase.local'],
          buttons: [
            {
              name: "purchase.local.add",
              meta: {
                icon: "ic:baseline-add",
                title: $t('basic.import'),
                perms: ['purchase.local.add'],
              }
            },
            {
              name: "purchase.local.view",
              meta: {
                icon: "ic:baseline-remove-red-eye",
                title: $t('basic.view'),
                perms: ['purchase.local.view'],
              }
            },
            {
              name: "purchase.local.confirm",
              meta: {
                icon: "ic:baseline-check-circle",
                title: $t('purchase.confirmOrder'),
                perms: ['purchase.local.confirm'],
              }
            },
            {
              name: "purchase.local.cancelConfirm",
              meta: {
                icon: "ic:baseline-cancel",
                title: $t('purchase.cancelConfirm'),
                perms: ['purchase.local.cancelConfirm'],
              }
            },
            {
              name: "purchase.local.delete",
              meta: {
                icon: "ic:round-delete",
                title: $t('basic.delete'),
                perms: ['purchase.local.delete'],
              }
            },
          ],

          order: 100,
        },
        name: 'local',
        path: '/Local/index',
        component: () => import('#/views/purchase/local/index.vue'),


      },

    ],
  },
];

export default routes;
