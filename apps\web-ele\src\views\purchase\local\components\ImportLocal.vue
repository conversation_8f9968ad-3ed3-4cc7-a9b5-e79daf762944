<template>
  <Modal class="w-[60%] h-[100%]" :title="title">
    <div class="mb-4">
      <ElUpload class="upload-demo" drag :show-file-list="false" accept=".xlsx, .xls" :limit="1"
        :on-exceed="handleExceed" :before-upload="handleBeforeUpload">
        <div class="el-upload__text">
          {{ $t('purchase.dragFile') }}<em>{{ $t('purchase.clickUpload') }}</em>
        </div>
        <template #tip>
          <div class="el-upload__tip text-right">
            {{ $t('purchase.importExcel') }}
            <ElButton type="primary" link @click="handleDownloadTemplate">
              {{ $t('purchase.downloadTemplate') }}
            </ElButton>
          </div>
        </template>
      </ElUpload>
    </div>

    <!-- 主表信息展示区域 -->
    <div v-if="masterData" class="mb-4 p-4 bg-gray-50 rounded-lg">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <span class="font-medium">{{ $t('purchase.orderDate') }}:</span>
          <span class="ml-2">{{ masterData.orderDate }}</span>
        </div>
        <div>
          <span class="font-medium">{{ $t('purchase.poNo') }}:</span>
          <span class="ml-2">{{ masterData.poNo }}</span>
        </div>
        <div>
          <span class="font-medium">{{ $t('purchase.declarationNo') }}:</span>
          <span class="ml-2">{{ masterData.declarationNo }}</span>
        </div>
        <div>
          <span class="font-medium">{{ $t('purchase.supplier') }}:</span>
          <span class="ml-2">{{ masterData.supplier }}</span>
        </div>
      </div>
    </div>

    <ClientGridComponent ref="gridRef" :columnDefs="columnDefs" :rowData="rowData" :pagination="false"
      style="height: 70%;" :defaultColDef="defaultColDef" :getRowClass="getRowClass" />
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ElUpload, ElButton } from 'element-plus';
import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import { useVbenModal } from '@vben/common-ui';
import { $t } from '#/locales';
import { useMessage } from '#/components/elementPlus/useMessage';
import { downLoadExcelModel,importByExcel,insertPurchase } from '#/api/purchase/imported';



/**
 * 组件状态
 * title: 弹窗标题
 * gridRef: 表格组件引用
 * rowData: 表格数据
 * masterData: 主表数据（订单级别信息）
 */
const title = $t('purchase.importLocal'); // 导入国外进口
const gridRef = ref();
const rowData = ref<any[]>([]);
const masterData = ref<any>(null);

/**
 * 表格列定义
 * 定义导入数据预览表格的列结构（仅包含明细数据）
 */
const columnDefs = [
  {
    headerName: $t('purchase.seq'),
    field: 'seq',
    minWidth: 80,
  },
  {
    headerName: $t('purchase.mCode'),
    field: 'mCode',
  },
   {
    headerName: $t('purchase.itemCode'),
    field: 'itemCode',
  },
   {
    headerName: $t('purchase.itemName'),
    field: 'itemName',
  },
   {
    headerName: $t('purchase.unit'),
    field: 'unit',
  },
   {
    headerName: $t('purchase.specModel'),
    field: 'specModel',
  },
  {
    headerName: $t('purchase.grossWeight'),
    field: 'grossWeight',
  },
  {
    headerName: $t('purchase.netWeight'),
    field: 'netWeight',
  },
  {
    headerName: $t('purchase.volume'),
    field: 'volume',
  },
  {
    headerName: $t('purchase.unitPrice'),
    field: 'unitPrice',
  },
  {
    headerName: $t('purchase.amount'),
    field: 'amount',
  },
  {
    headerName: $t('purchase.currency'),
    field: 'currency',
  },
  {
    headerName: $t('production.errorMsg'),
    field: 'errorMsg',
  },
];

/**
 * 默认列定义
 * 所有列的通用配置
 */
const defaultColDef = {
  sortable: true,  // 可排序
  filter: true,    // 可筛选
  resizable: true, // 可调整大小
  minWidth:  70,    // 最小宽度
};

/**
 * 处理行样式
 * 根据数据状态设置行的样式类
 * @param params 行参数
 * @returns 样式类名
 */
const getRowClass = (params: { data: { hasErrorMsg?: boolean } }) => {
   // 这里可以自定义逻辑，比如根据某些条件返回类名
   if (params.data.hasErrorMsg) {
    console.log(params.data.hasErrorMsg);
    return 'import-error';
  }
  return ''; // 默认不应用样式
};

/**
 * 自动调整所有列宽
 * 根据列内容自动调整列宽度
 */
const autoSizeAllColumns = () => {
  if (gridRef.value?.gridApi) {
    // 自动调整所有列的宽度以适应内容
    gridRef.value.gridApi.autoSizeAllColumns();
  }
};

/**
 * 调整列宽以适应容器
 * 让所有列填满容器宽度
 */
const sizeColumnsToFit = () => {
  if (gridRef.value?.gridApi) {
    // 调整列宽以适应容器宽度
    gridRef.value.gridApi.sizeColumnsToFit();
  }
};

/**
 * 处理超出上传限制
 * 当上传文件数量超过限制时触发
 */
const handleExceed = () => {
  useMessage().showMessage('warning', $t('purchase.exceedLimit'));
};

/**
 * 处理上传前的验证
 * 在文件上传前进行验证和处理
 * @param file 上传的文件对象
 * @returns 是否继续上传
 */
const handleBeforeUpload = (file: File) => {
  // 验证文件类型是否为Excel
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel';

  // 如果不是Excel文件，显示警告并阻止上传
  if (!isExcel) {
    useMessage().showMessage('warning', $t('purchase.onlyExcel'));
    return false;
  }

  // 锁定弹窗，防止重复操作
  modalApi.lock();

  // 创建FormData对象并添加文件
  const formData = new FormData();
  formData.append('InputStream', file);

  // 调用API上传文件
  importByExcel(formData).then((res) => {
    // 上传成功，显示成功消息并更新表格数据
    useMessage().showMessage('success', $t('purchase.importSuccess'));

    // 处理返回的数据，根据PurchaseOrderExcelDto结构处理
    if (res && res.purchaseOrder && res.purchaseOrderDetail) {
      // 设置主表数据
      masterData.value = {
        orderDate: res.purchaseOrder.orderDate,
        poNo: res.purchaseOrder.poNo,
        declarationNo: res.purchaseOrder.declarationNo,
        supplier: res.purchaseOrder.supplier,
      };

      // 设置明细数据 - 直接赋值
      rowData.value = res.purchaseOrderDetail;

      // 数据加载完成后自动调整列宽
      setTimeout(() => {
        autoSizeAllColumns();
      }, 100);
    } else {
      rowData.value = [];
      masterData.value = null;
    }
  }).catch((error) => {
    // 上传失败，显示错误消息
    useMessage().showMessage('error', $t('purchase.importFailed'));
    console.error('Import failed:', error);
  }).finally(() => {
    // 无论成功失败，都解锁弹窗
    modalApi.unlock();
  });

  return false; // 阻止 ElUpload 的自动上传，由我们手动处理
};

/**
 * 下载导入模板
 * 获取用于批量导入的Excel模板文件
 */
const handleDownloadTemplate = async () => {
  try {
    downLoadExcelModel().then((response) => {

      // 解码 Base64 字符串
      const byteCharacters = atob(response.fileContent);

      // 将 Base64 字符串转换为字节数组
      const byteArrays = [];
      for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
        const slice = byteCharacters.slice(offset, offset + 1024);
        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }

      const file = new Blob(byteArrays, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      const a = document.createElement('a');
      a.style.display = 'none';
      const fileURL = window.URL.createObjectURL(file);
      a.href = fileURL;
      a.download = response.fileName;
      document.body.appendChild(a);
      a.click();
      // 释放 URL 对象
      window.URL.revokeObjectURL(fileURL);
      document.body.removeChild(a);
    });
  } catch (error) {
    // 下载失败，显示错误消息
    console.error($t('purchase.downloadTemplateFailed'), error);
    useMessage().showMessage('error', $t('purchase.downloadTemplateFailed'));
  }
};

/**
 * 保存导入的数据
 * 将预览表格中的数据保存到数据库
 */
const saveData = async () => {
  // 检查是否有数据可保存
  if (rowData.value.length === 0) {
    useMessage().showMessage('warning', $t('purchase.noDataToSave'));
    return;
  }

  // 检查是否有错误数据
  const hasError = rowData.value.some(item => item.hasError);
  if (hasError) {
    useMessage().showMessage('warning', $t('purchase.hasErrorData'));
    return;
  }

  try {
    // 这里应该调用保存导入数据的API
    await insertPurchase({purchaseOrder:masterData.value, purchaseOrderDetail: rowData.value,orderType:"local" });

    // 保存成功，显示成功消息并关闭弹窗
    useMessage().showMessage('success', $t('purchase.saveSuccess'));
    modalApi.close();
  } catch (error) {
    // 保存失败，显示错误消息
    useMessage().showMessage('error', $t('purchase.saveFailed'));
  }
};

/**
 * 创建模态框
 * 配置模态框的行为和事件处理
 */
const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false, // 点击遮罩层不关闭
  draggable: true, // 可拖动
  destroyOnClose: true, // 关闭时销毁内容
  // 打开状态变化时的回调
  onOpenChange(isOpen) {
    if (!isOpen) {
      // 关闭时清空数据
      rowData.value = [];
      masterData.value = null;
    }
  },
  // 取消按钮点击回调
  onCancel() {
    modalApi.close();
  },
  // 确认按钮点击回调
  onConfirm() {
    saveData();
  }
});

/**
 * 暴露组件方法
 * 直接暴露 modalApi，包含所有需要的方法
 */
defineExpose({
  modalApi,
});
</script>
