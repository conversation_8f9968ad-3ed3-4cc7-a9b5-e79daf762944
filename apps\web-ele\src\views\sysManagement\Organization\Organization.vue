<script lang="ts" setup>
import { ref } from 'vue';

import { type GridApi, type GridOptions, AllCommunityModule, ModuleRegistry } from 'ag-grid-community';
import { TreeDataModule } from 'ag-grid-enterprise';

import { ElCard,ElNotification } from "element-plus";
import { useVbenForm } from '#/adapter/form';
import { Page} from '@vben/common-ui';
import { AgGridVue } from 'ag-grid-vue3';
import CreateOrEdit from './CreateOrEdit.vue';
import { getOrgList,deleteOrganization } from '#/api/sys/organization'

import ActionCell from '#/components/ag-grid/ButtonRender.vue';

import { TableAction } from '#/components/table-action/index.js';

ModuleRegistry.registerModules([AllCommunityModule, TreeDataModule]);

//import ActionCell from '@/components/ActionCell.vue';
const gridApi = ref<GridApi>();
const createOrEditRef = ref();

const isExpand = ref(true);

const components = {
  actionCell: ActionCell, // 这里的 key 是你在 cellRenderer 中使用的值
};






const gridOptions: GridOptions = {
  columnDefs: [
    // { headerName: '部门名称', field: 'name' },
    { headerName: '负责人', field: 'leader' },
    { headerName: '联系电话', field: 'telephone' },
    { headerName: '排序', field: 'sort' },
    {
      headerName: "操作",
      field: "action", // 注意这里field不是实际的数据字段，仅用于标识
      pinned: 'right',
      cellRenderer: 'actionCell',
      width: 150,
      cellRendererParams: {
        actions: [
          {
            label: '编辑',
            callback: (data: any) => {
              console.log('编辑操作:', data);
              //执行编辑操作
              openEditModal(data);
            },
            type: 'primary',
            size: 'small',
            //eventName: 'editEvent' // 自定义事件名
          },
          {
            label: '删除',
            callback: async (data: any) => {
              //删除
              await deleteOrganization({ id: data.id });
              ElNotification({ duration: 2500, message: '执行成功！', type: 'success' });
              await getData();
            },
            type: 'danger',
            size: 'small',
            //eventName: 'deleteEvent' // 自定义事件名
          },
        ]
      }
    },
  ],
  rowData: [

  ],
  groupDefaultExpanded: -1,
  getDataPath: (data) => {
    const path = [];
    let currentData = data;

    // Traverse upwards and build the path
    while (currentData) {
      path.unshift(currentData.id); // Add current node's id to the start of the path
      currentData = rowData.value.find(item => item.id === currentData.parentId); // Find parent node in allData
    }

    return path;
  },
  autoGroupColumnDef: {
    headerName: "组织结构",
    field: "name",
    cellRendererParams: { suppressCount: true } // 不显示子项数量
  },
  pagination: true,
  paginationPageSizeSelector: [20, 50, 100],
  defaultColDef: {
    flex: 1,
  },
  treeData: true,
  rowModelType: 'clientSide',
  onGridReady: (params) => {
    gridApi.value = params.api;
    getData();
  },
};

const rowData = ref<any[]>([]);


async function getData() {
  try {
    // 等待 sysUserGet 返回结果
    const res = await getOrgList({});
    if (Array.isArray(res)) {
      // 确保 res 是数组类型
      rowData.value = res;

      // 更新 AG Grid 数据
      gridApi.value!.setGridOption("rowData", res);
    }
  } catch (error) {
    console.error('Error fetching data:', error);  // 捕获并打印错误
  }
}

const [QueryForm] = useVbenForm({
  handleSubmit: (values) => {
    getData();
  },
  submitButtonOptions: {
    content: '查询',
  },
  resetButtonOptions: {
    content: "重置",
  },
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  collapsed: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  schema: [
    {
      label: '角色代码',
      component: 'Input',
      fieldName: 'roleCode',
    },
    {
      label: '角色名称',
      component: 'Input',
      fieldName: 'roleName',
    },
    {
      label: '备注',
      component: 'Input',
      fieldName: 'remark',
    },
  ]
});




const modules = [AllCommunityModule];

function openAdd() {
  createOrEditRef.value.setData({
    isUpdate: false,
    record: [],
  });
  createOrEditRef.value.open();
  createOrEditRef.value.onClosed = () => {
    // 调用刷新数据的方法
    getData();
  };
}

function openEditModal(data: any) {
  createOrEditRef.value.setData({
    isUpdate: true,
    record: data,
  });
  createOrEditRef.value.open();
  createOrEditRef.value.onClosed = () => {
    // 调用刷新数据的方法
    getData();
  };
}
//展开
function expandAll() {
  if(gridApi.value)
  {
    const allNodes = gridApi.value.getRenderedNodes();
    if (allNodes) {
      allNodes.forEach(node => node.setExpanded(!isExpand.value));
      isExpand.value = !isExpand.value;
    }
  }
  
}


</script>

<template>
  <page>
    <ElCard>
      <TableAction
          :actions="[
            {
              label: '新增',
              type: 'primary',
              icon: 'material-symbols:add-2-rounded',
              //auth: ['admin', 'sys:dept:save'],
              onClick: openAdd.bind(null),
            },
            {
              label: isExpand ? '折叠' : '展开',
              type: 'primary',
              icon: isExpand ? 'ep:folder-opened' : 'ep:folder',
              onClick: expandAll.bind(null),
            },
          ]"
        />
      <!-- <QueryForm /> -->
      <!-- <ElButton type="primary" @Click="openAdd">
        <IconifyIcon icon="material-symbols:add-2-rounded" />
        新增
      </ElButton> -->
      <div style="margin-top: 20px;">
        <AgGridVue :gridOptions="gridOptions" class="ag-theme-alpine" style="width: 100%; height: 75vh;"
          :components="components" :modules="modules">
        </AgGridVue>
      </div>
    </Elcard>


    <CreateOrEdit ref="createOrEditRef" />
  </page>




</template>
