<template>
  <PageCard auto-content-height>
    <QueryForm />
    <TableAction :actions="[
      {
        label: $t('basic.add'),
        type: 'primary',
        icon: 'ep:plus',
        auth: ['product.product.add'],
        onClick: handleAdd.bind(null),
      },
    ]" />

    <ClientGridComponent ref="gridRef" :columnDefs="columnDefs" :rowData="rowData" />
    <productDetail ref="productDetailRef" />
  </PageCard>
  

</template>
<script lang="ts" setup>
import {  ElMessage } from "element-plus";
import { PageCard } from '@vben/common-ui';
import { TableAction } from '#/components/table-action';
import { useVbenForm } from '#/adapter/form';
import { $t } from '#/locales';
import { ref, onMounted } from 'vue';
import { getProduct,deleteProduct } from '#/api/production/product';
import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import productDetail from './productDetail.vue';
const productDetailRef = ref();

const [QueryForm] = useVbenForm({
  handleSubmit: (values) => {
    getData(values);
  },
  submitButtonOptions: {
    content: $t('basic.search'),
  },
  resetButtonOptions: {
    content: $t('basic.reset'),
  },
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  collapsed: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  schema: [
    {
      label: $t('product.productCode'),
      component: 'Input',
      fieldName: 'hsCode',
    },
    {
      label: $t('product.productName'),
      component: 'Input',
      fieldName: 'productName',
    },
    {
      label: $t('product.Category'),
      component: 'Input',
      fieldName: 'category',
    },
  ]
});

const gridRef = ref<any>(null);
const rowData = ref<any[]>([])

const handleAdd = (data: any) => {
  //获取到勾选数据

  productDetailRef.value.setData({
    record: data,
    isUpdate: false,
    isView: false,
  });
  productDetailRef.value.open();
  productDetailRef.value.onClosed = () => {
    //调用刷新数据的方法
    getData();
  };

}
const handleEdit = (data: any, pageType: String) => {

  productDetailRef.value.setData({
    record: data,
    pageType: pageType,
  });
  productDetailRef.value.open();
  productDetailRef.value.onClosed = () => {
    // 调用刷新数据的方法
    getData();
  };

}

//删除
const handleDelete = async (data: any) => {
  try {
    await deleteProduct({ id: data.id }); // 假设 data.id 是要删除的记录的 ID
    // 这里可以添加一个提示框，使用 Element Plus 的 Message 组件
    ElMessage.success($t('production.DeleteSuccess')); // 提示删除成功
    getData(); // 重新获取数据以更新表格
  } catch (error) {
    ElMessage.error($t('production.DeleteFailed')); // 提示删除失败
  }
}

const columnDefs: any[] = [
  { headerName: $t('product.productCode'), field: 'hsCode', width: 115 },
  { headerName: $t('product.productName'), field: 'productName', width: 125 },
  { headerName: $t('product.productDesc'), field: 'productDec', width: 115 },
  { headerName: $t('product.Category'), field: 'category', width: 125 },
  { headerName: $t('product.subCategory'), field: 'subCategory', width: 125 },
  { headerName: $t('product.productUnit'), field: 'meterUnit', width: 155 },
  { headerName: $t('product.tradingUnit'), field: 'tradUnit', width: 135 },
  { headerName: $t('product.remark'), field: 'Remark', width: 135 },
  { headerName: $t('production.Updater'), field: 'updateUserName', width: 125 },
  { headerName: $t('production.UpdateDate'), field: 'updateTime', width: 125 },
  {
    headerName: $t('production.Action'),
    field: "action", // 注意这里field不是实际的数据字段，仅用于标识
    pinned: 'right',
    cellRenderer: 'actionCell',
    flex: 1,
    cellRendererParams: {
      actions: [
        {
          label: $t('basic.edit'),
          callback: (data: any) => {
            handleEdit(data, 'edit')
          },
          auth: ['product.product.edit'],
          type: 'primary',
          size: 'small',
          //disabled: (params: any) => params.data.status !== 'new' && params.data.status !== 'cancel', // Disable if status is not 'new' or 'cancel'
          //eventName: 'editEvent' // 自定义事件名
        },
        {
          label: $t('basic.view'),
          callback: (data: any) => {
            handleEdit(data, 'view')
          },
          auth: ['product.product.view'],
          type: 'primary',
          size: 'small',
          //eventName: 'editEvent' // 自定义事件名
        },
        {
          label: $t('basic.delete'),
          callback: (data: any) => {
            handleDelete(data)
          },
          auth: ['product.product.delete'],
          type: 'danger',
          size: 'small',
          //disabled: (params: any) => params.data.status !== 'new', // Disable if status is not 'new'
        },
      ]
    }
  },

];
async function getData(data: any | [] = []) {
  const res = await getProduct({
    ...data
  });
  if (Array.isArray(res)) {
    rowData.value = res;
    setTimeout(() => {
        if (gridRef.value?.gridApi) {
          gridRef.value.gridApi.autoSizeAllColumns();
        }
      }, 10);
  }

}
onMounted(() => {
  getData();
});
</script>
