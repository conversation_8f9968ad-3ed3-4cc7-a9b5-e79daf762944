<script lang="ts" setup>
import { ref } from 'vue';

import { PageCard } from '@vben/common-ui';
import {
  ElMessage,
  ElNotification
} from 'element-plus';

import { TableAction } from '#/components/table-action';

import { useVbenForm } from '#/adapter/form';
import type { GridOptions, GridApi } from 'ag-grid-community';
import { AgGridVue } from 'ag-grid-vue3';  // 引入 AG Grid 组件
import { AllCommunityModule, ModuleRegistry, LocaleModule, ClientSideRowModelModule, ValidationModule,type ICellRendererParams  } from 'ag-grid-community';
import {
  ColumnMenuModule,
  ContextMenuModule,
  FiltersToolPanelModule,
  SetFilterModule, MultiFilterModule
} from 'ag-grid-enterprise';
import { sysUserGet, setAccountStatus } from '#/api/sys/user';
import UserInfoModal from './userCreateOrEdit.vue';
import UserRole from './user-role.vue';

import ActionCell from '#/components/ag-grid/ButtonRender.vue';

ModuleRegistry.registerModules([
  LocaleModule,
  ClientSideRowModelModule,
  FiltersToolPanelModule,
  ColumnMenuModule,
  ContextMenuModule,
  SetFilterModule,
  MultiFilterModule,
  ValidationModule /* Development Only */,
]);

const userRoleRef = ref();

const columnDefs = [
  {
    headerName: "真实姓名",
    field: "name",
    filter: 'agMultiColumnFilter', filterParams: {
      // can be 'windows' or 'mac'
      excelMode: 'mac',
    },
  },
  { headerName: "账号", field: "account" },
  { headerName: "手机号", field: "telephone" },
  { headerName: "邮箱", field: "email" },
  {
    headerName: "状态", field: "status",
    cellRenderer: (params : ICellRendererParams) => {
      const icon = params.value == "1" ? "✅" : "❌";  // 你可以替换为其他图标
      return icon;
    }
  },
  { headerName: "部门", field: "organizationName" },
  { headerName: " 备注", field: "remark" },
  {
    headerName: "操作",
    field: "action", // 注意这里field不是实际的数据字段，仅用于标识
    pinned: "right" as const,
    cellRenderer: 'actionCell',
    width: 500,
    cellRendererParams: {
      actions: [
        {
          label: '编辑',
          callback: async (data: any) => {
            //执行编辑操作
            await openEditModal(data);
          },
          auth: ['user.edit'],
          type: 'primary',
          size: 'small',
          //eventName: 'editEvent' // 自定义事件名
        },
        {
          label: (params: any) => params.data.status == '0' ? '启用' : '禁用', // 动态生成标签, 
          callback: async (data: any) => {
            //账号启用禁用
            await setAccountStatus({ id: data.id, status: data.status });
            ElNotification({ duration: 2500, message: '执行成功！', type: 'success' });
            await getData();
          },
          auth: ['user.delete'],
          type: (params: any) => params.data.status == '0' ? 'success' : 'danger',
          size: 'small',
          //eventName: 'deleteEvent' // 自定义事件名
        },
        {
          label: '授权角色',
          callback: async (data: any) => {
            //执行编辑操作
            handleUserRole(data);
          },
          auth: ['user.role'],
          type: 'default',
          size: 'small',
          //eventName: 'editEvent' // 自定义事件名
        },

      ]
    }
  },
];
// 数据定义
const rowData = ref<any[]>([]);

const openUserApi = ref();

const components = {
  actionCell: ActionCell, // 这里的 key 是你在 cellRenderer 中使用的值
};

const defaultColDef = {
  flex: 1,
};
const gridApi = ref<GridApi | null>(null);

const gridOptions: GridOptions = {
  columnDefs: columnDefs,
  rowData: rowData.value,
  defaultColDef: defaultColDef,
  pagination: true,
  components: components,
  paginationPageSizeSelector: [20, 50, 100],
  rowModelType: 'clientSide',
  onGridReady: (params) => {
    gridApi.value = params.api;
    //调用方法
    getData();
  },
};
const [QueryForm] = useVbenForm({
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-6',
  collapsed: false,
  //提交时间
  handleSubmit: (values) => {
    //console.log('values', values);
    ElMessage.success(`表单数据：${JSON.stringify(values)}`);
  },
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: [{
    label: "账号",
    component: 'Input',
    fieldName: 'accound',
  },
  { label: "真实姓名", component: 'Input', fieldName: 'name', },
  { label: "手机号", component: 'Input', fieldName: 'telephone', },
  { label: "邮箱", component: 'Input', fieldName: 'email', },
  {
    label: "状态", component: 'Select', fieldName: 'status',
    componentProps: {
      allowClear: true,
      filterOption: true,
      options: [
        {
          label: '未启用',
          value: '0',
        },
        {
          label: '启用',
          value: '1',
        }],
    },

  }]
});

const openInContentModal = () => {
  openUserApi.value.setData({
    isUpdate: false,
    record: {},
  });
  openUserApi.value.open();
  openUserApi.value.onClosed = () => {
    // 调用刷新数据的方法
    getData();
  };
};

function openEditModal(data: any) {
  openUserApi.value.setData({
    isUpdate: true,
    record: data,
  });
  openUserApi.value.open();
  openUserApi.value.onClosed = () => {
    // 调用刷新数据的方法
    getData();
  };
}
async function getData() {
  try {
    // 等待 sysUserGet 返回结果
    const res = await sysUserGet({});
    if (Array.isArray(res)) {
      // 确保 res 是数组类型
      rowData.value = res;
      // 更新 AG Grid 数据
      gridApi.value?.setGridOption("rowData", res);
    }
  } catch (error) {
    console.error('Error fetching data:', error);  // 捕获并打印错误
  }
}
// 授权角色
const handleUserRole = (record: any) => {
  userRoleRef.value.setData({
    record,
  });
  userRoleRef.value.open();
};

const modules = [AllCommunityModule];
</script>
<template>
  <PageCard auto-content-height>
      <QueryForm />
      <TableAction :actions="[
      {
        label: $t('production.Add'),
        type: 'primary',
        icon: 'ep:plus',
        auth: ['user.add'],
        onClick: openInContentModal.bind(null),
      }

    ]">
    </TableAction>
      <div style="margin-top: 20px; height: 100%;" >
        <AgGridVue :gridOptions="gridOptions" class="ag-theme-alpine" style="width: 100%; height: 100%;"
          :modules="modules">
        </AgGridVue>
      </div>
    <div>
      <UserInfoModal ref="openUserApi" />
    </div>

    <UserRole ref="userRoleRef" />
  </PageCard>

</template>
