import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'material-symbols:person-3',
      keepAlive: true,
      order: 1000,
      title: '个人中心',
      hideInMenu: true,
      ignoreSync: true,
      component: 'BasicLayout',
      ignoreAccess: true,
      //perms: ['sysManagement.personal'],
    },
    name: 'personal:manager',
    path: '/sysManagement/manager',
    children: [
      {
        meta: {
          icon: 'material-symbols:person-3',
          title: '个人中心',
          hideInMenu: true,
          ignoreSync: true,
          ignoreAccess: true,
          component: '/sysManagement/personal/index',
          //perms: ['sysManagement.personal.main'],
        },
        name: 'sys:personal',
        path: '/sysManagement/personal/index',
        component: () => import('#/views/sysManagement/personal/index.vue'),
      },
    ],
  },
];

export default routes;
