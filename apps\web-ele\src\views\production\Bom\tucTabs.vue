<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';

import tucInfo from './tucInfo.vue';
import tucMCode from './tucMCode.vue';


import {
  ElTabs as Tabs,
  ElTabPane as TabPane,
} from 'element-plus';
import { $t } from '#/locales';


const record = ref();
const isUpdate = ref(false);
const isView = ref(false);

const updateRecord = (newRecord: any) => {
  record.value = newRecord; // 更新 record 的值
};

const activeTab = ref('1');
const handleChange = (key: any) => {
  if (key === '2') {

  }
};

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  //隐藏确认按钮
  showConfirmButton: false,
  draggable: true,
  loading: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      record.value = modalApi.getData()?.record || {};
      isUpdate.value = modalApi.getData()?.pageType === 'edit';
      isView.value = modalApi.getData()?.pageType === 'view';
      modalApi.setState({ loading: false });
    }
  },
  onCancel() {
    modalApi.close();
  },
});

defineExpose(modalApi);
</script>
<template>
  <Modal class="w-[70%]" title="成品">
    <div class="h-[70%] min-h-[500px]">
      <Tabs v-model="activeTab" @change="handleChange" class="h-[100%]">
        <TabPane :label="$t('production.ProductInfo')" name="1" class="flex-1">
          <tucInfo :id="record.id" :isUpdate="isUpdate" :isView="isView" :updateRecord="updateRecord" />
        </TabPane>
        <TabPane :label="$t('production.MCodeInfo')" name="2" style="height: 500px">
          <tucMCode :fgCode="record.fgCode" :isView="isView" />
        </TabPane>
      </Tabs>
    </div>
  </Modal>
</template>
<style>


</style>
