import { useAccessStore } from '@vben/stores';

/**
 * 从后端接口获取图片并转换为Base64
 * @param imageUrl 图片URL（后端返回的相对路径）
 * @returns Promise<string> Base64字符串
 */
export async function getImageAsBase64(imageUrl: string): Promise<string> {
  if (!imageUrl) return '';
  
  // 如果已经是Base64格式，直接返回
  if (imageUrl.startsWith('data:image')) {
    return imageUrl;
  }

  // 如果是完整的http(s)链接，直接返回
  if (imageUrl.startsWith('http')) {
    return imageUrl;
  }

  try {
    const accessStore = useAccessStore();
    const baseUrl = import.meta.env.VITE_GLOB_API_URL.replace(/\/+$/, '');
    const fullUrl = `${baseUrl}/${imageUrl.replace(/^\/+/, '')}`;

    const response = await fetch(fullUrl, {
      headers: {
        Authorization: `Bearer ${accessStore.accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch image');
    }

    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Error converting image to base64:', error);
    // 返回一个默认的头像Base64或URL
    return import.meta.env.VITE_DEFAULT_AVATAR || '';
  }
} 
