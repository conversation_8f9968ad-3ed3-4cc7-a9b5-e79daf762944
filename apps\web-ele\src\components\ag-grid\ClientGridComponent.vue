<template>
  <div ref="tableRef" class="relative w-full h-full">
    <!-- TableAction 区域 -->
    <div v-if="tableActions && tableActions.length > 0" class="mb-4">
      <div class="flex justify-between items-center">
        <!-- 左侧：TableAction 按钮组 -->
        <TableAction :actions="tableActions" />

        <!-- 右侧：表格操作按钮 -->
        <div class="flex gap-2">
          <!-- 刷新按钮 -->
          <Button shape="circle" size="small" @click="handleRefresh" title="刷新表格">
            <Icon icon="ant-design:reload-outlined" />123213
          </Button>
          <!-- 全屏切换按钮 -->
          <Button shape="circle" size="small" @click="toggle" :title="isFullscreen ? '退出全屏' : '进入全屏'">
            <Icon :icon="isFullscreen ? 'ant-design:fullscreen-exit-outlined' : 'ant-design:fullscreen-outlined'" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 只有表格操作按钮的情况（没有 TableAction） -->
    <div v-else class="absolute top-4 right-4 z-10 flex gap-2">
      <!-- 刷新按钮 -->
      <Button shape="circle" size="small" @click="handleRefresh" title="刷新表格">
        <Icon icon="ant-design:reload-outlined" />
      </Button>
      <!-- 全屏切换按钮 -->
      <Button shape="circle" size="small" @click="toggle" :title="isFullscreen ? '退出全屏' : '进入全屏'">
        <Icon :icon="isFullscreen ? 'ant-design:fullscreen-exit-outlined' : 'ant-design:fullscreen-outlined'" />
      </Button>
    </div>

    <!-- ag-grid 表格 -->
    <ag-grid-vue style="margin-top:5px"
      class="ag-theme-alpine"
      :gridOptions="mergedGridOptions"
      :columnDefs="columnDefs"
      :rowData="rowData"
      :localeText="localeText"
      :pagination="true"
      :modules="modules"
      :height="height"
      :rowSelection="rowSelection"
      :components="components"
      :defaultColDef="defaultColDef"
      :rowClassParams="rowClassParams"
      :getRowClass="getRowClass"
      :style="{ width: '100%', height: '100%', minHeight: '300px' }"
      @grid-ready="onGridReady"
    />
  </div>
</template>


<!-- :domLayout="'autoHeight'" -->
<script lang="ts" setup>
import { ref, defineProps, watch, defineExpose, type PropType, onMounted, computed } from 'vue';
import { AgGridVue } from 'ag-grid-vue3';
import { type GridOptions, type ColDef, type FilterModel, type GridApi, AllCommunityModule, type RowSelectionOptions, type RowClassParams } from 'ag-grid-community';
import {
  CellSelectionModule,
  ContextMenuModule,
  ColumnMenuModule,
  ClipboardModule,
  ExcelExportModule
} from "ag-grid-enterprise";
import ActionCell from '#/components/ag-grid/ButtonRender.vue';
import { preferences } from '@vben/preferences';
import type { SupportedLanguagesType } from '@vben/locales';
import { TableAction, type ActionItem } from '#/components/table-action';
type GetRowClass = (params: any) => string | string[] | undefined;
const gridApi = ref<GridApi | null>(null);
const localeText = ref<any>(undefined);
import { useFullscreen } from '@vueuse/core'; // ✅ 使用 VueUse 全屏 API

const tableRef = ref<HTMLElement | null>(null);
const { isFullscreen, toggle } = useFullscreen(tableRef);

// 示例刷新事件（你可以换成 emit 或 gridApi 调用）
const handleRefresh = () => {
  if (gridApi.value) {
    gridApi.value!.refreshCells();
  }
};

// 进入全屏时自动调整表格宽度
watch(isFullscreen, (val) => {
  if (val && gridApi.value) {
    setTimeout(() => {
      gridApi.value!.sizeColumnsToFit();
    }, 300);
  }
});

// 加载AG Grid的语言包
async function loadAgGridLocale(lang: SupportedLanguagesType) {
  let localeTextValue;

  switch (lang) {
    case 'en-US': {
      const { AG_GRID_LOCALE_EN } = await import('@ag-grid-community/locale');
      localeTextValue = AG_GRID_LOCALE_EN;
      break;
    }
    case 'zh-CN': {
      const { AG_GRID_LOCALE_CN } = await import('@ag-grid-community/locale');
      localeTextValue = AG_GRID_LOCALE_CN;
      break;
    }
    case 'vi-VN': {
      const { AG_GRID_LOCALE_VN } = await import('@ag-grid-community/locale');
      localeTextValue = AG_GRID_LOCALE_VN;
      break;
    }
    default: {
      const { AG_GRID_LOCALE_EN } = await import('@ag-grid-community/locale');
      localeTextValue = AG_GRID_LOCALE_EN;
      break;
    }
  }

  return localeTextValue;
}

// 组件接收的 props
const props = defineProps({
  gridOptions:{
    type: Object as () => GridOptions,
    default: () => ({
      rowModelType: 'clientSide',
      pagination: true,
      suppressPaginationPanel: false,
      cellSelection: true,
      copyHeadersToClipboard: false,
      editType: 'fullRow',
    }),
  },
  columnDefs: {
    type: Array as () => ColDef[],
    default: [],
  },
  rowData: {
    type: Array as () => any[],
    default: [],
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  filterModel: {
    type: Object as () => FilterModel | null,
    default: () => null,
  },
  defaultColDef: {
    type: Object as () => ColDef,
    default: () => ({
      sortable: true,
      filter: true,
      resizable: true,
      flex: 1,
    }),
  },
  modules :{
    type: Array as () => any[],
    default: () => [AllCommunityModule, CellSelectionModule, ContextMenuModule, ColumnMenuModule, ClipboardModule,ExcelExportModule],
  },
  rowSelection: {
    type: Object as () => RowSelectionOptions,
    default: () => ({
      mode: 'singleRow',  // singleRow 默认单选//multiRow
      checkboxes: false, // 默认关闭
      headerCheckbox: false, // 默认关闭
      enableClickSelection: false,
      copySelectedRows: false,
    }),
  },
  rowClassParams: {
    type: Object as () => RowClassParams,
    default: () => ({}),
  },
  getRowClass: {
    type: Function as PropType<GetRowClass>,
    default: () => (_params: any) => {
      // 默认行为：返回空的字符串或一个空的数组
      return '';
    },
  },

  height: {
    type: String,
    default: '95%',
  },
  tableActions: {
    type: Array as PropType<ActionItem[]>,
    default: () => [],
  },
});

// 表格数据（从 props 获取）
const rowData = ref<any[]>(props.rowData);

const components = {
  actionCell: ActionCell, // 这里的 key 是你在 cellRenderer 中使用的值
};

// 合并gridOptions和localeText
const mergedGridOptions = computed(() => {
  return {
    ...props.gridOptions,
    paginationPageSize: props.pageSize,
  };
});


// 初始化语言设置
onMounted(async () => {
  // 加载当前语言的AG Grid本地化文本
  localeText.value = await loadAgGridLocale(preferences.app.locale);

});

// 监听语言变化
watch(() => preferences.app.locale, async (newLocale) => {
  // 加载新语言的AG Grid本地化文本
  localeText.value = await loadAgGridLocale(newLocale as SupportedLanguagesType);
});

// 事件：Grid Ready 时加载数据
const onGridReady = (params: any) => {
  // 初始化数据，调用一次处理方法
  gridApi.value = params.api;

  // 调用从 props 传入的 onGridReady 回调（如果存在）
  if (props.gridOptions?.onGridReady) {
    props.gridOptions.onGridReady(params);
  }
};

// 监听 props 变化（如果需要）
watch(() => props.rowData, (newData) => {
  rowData.value = newData;
});
// 暴露全屏相关功能和 gridApi
defineExpose({
  gridApi,
  isFullscreen,
  toggle,
  handleRefresh
});
</script>

<style>
 .import-error{
  background-color:#f8d7da !important;
 }
</style>
