<template>
  <TableAction :actions="[
    {
      label: $t('production.Add'),
      type: 'primary',
      icon: 'ep:plus',
      auth: ['tucfp.add'],
      ifShow: !props.isView,
      onClick: handleAdd.bind(null),
    },
    {
      label: $t('production.Delete'),
      type: 'danger',
      icon: 'ep:delete',
      auth: ['tucfp.delete'],
      ifShow: !props.isView,
      onClick: handleDelete.bind(null),
    },
  ]">
  </TableAction>
  <div class="flex-1 h-full" style = "minHeight:400px">
    <ClientGridComponent
      ref="gridRef"
      :columnDefs="columnDefs"
      :rowData="rowData"
      :pageSize="20"
      :defaultColDef="defaultColDef"
      :rowSelection="rowSelection"
      :gridOptions="gridOptions"
      style="height: 100%;"
    />
  </div>

  <addMCode ref="addMCodeRef" />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import { TableAction } from '#/components/table-action';
import { $t } from '#/locales';

import { getTechConsumptionList, deleteBomMCode, updateTechConsumption } from '#/api/production/bom'

import addMCode from './addMCode.vue';
import { useMessage } from '#/components/elementPlus/useMessage';

// 接收父组件传递的属性
const props = defineProps<{
  fgCode?: string;
  isView?: boolean;
}>();

const gridRef = ref();
const editingRowId = ref<string | null>(null);

async function getData(data: any | [] = []) {
  const res = await getTechConsumptionList({
    ...data
  });
  if (Array.isArray(res)) {
    // 确保 res 是数组类型
    rowData.value = res;
  }
}

// 更新计算值
const updateCalculatedValues = (data: any, changedField: string, api: any, node: any) => {
  if (!data) return;

  // 确保所有输入值都不为负数
  const numericFields = ['originalUnit', 'lossRate', 'unitConsumption', 'cusProvidedKit',
    'cusProvidedMopClothLength', 'cusProvidUnitConsumption', 'fullPageSets',
    'effectiveUsageRate', 'mopLength', 'casing'];

  numericFields.forEach(field => {
    if (data[field] !== undefined && data[field] !== null && data[field] !== '') {
      const value = typeof data[field] === 'string' ? parseFloat(data[field]) : data[field];
      if (value < 0) data[field] = 0;
    }
  });

  const originalUnit = Number(data.originalUnit || 0);
  const unitConsumption = Number(data.unitConsumption || 0);

  // 根据不同的字段变化进行相应的计算
  if (changedField === 'originalUnit' || changedField === 'lossRate') {
    // 如果原始单耗或损耗率变化，计算单耗
    const lossRate = Number(data.lossRate || 0);
    data.unitConsumption = calculateUnitConsumption(originalUnit, lossRate);
  } else if (changedField === 'unitConsumption' && originalUnit > 0) {
    // 如果单耗变化，计算损耗率
    data.lossRate = calculateLossRate(unitConsumption, originalUnit);
  }

  // 刷新所有相关单元格
  if (api) {
    api.refreshCells({
      rowNodes: [node],
      columns: ['originalUnit', 'lossRate', 'unitConsumption']
    });
  }
}

// 定义 AG Grid 的事件处理函数
const gridOptions: any = {
  // 单元格编辑开始事件
  onCellEditingStarted: (params: any) => {
    if (params.data && params.data.id) {
      editingRowId.value = params.data.id;
    }
  },

  // 单元格值变化事件 - 当单元格值变化时触发
  onCellValueChanged: (params: any) => {
    if (!params.data || params.newValue === params.oldValue) return;

    // 检查输入值是否为负数
    if (params.newValue !== null && params.newValue !== '' && parseFloat(params.newValue) < 0) {
      // 如果是负数，恢复为原值或设为0
      params.data[params.column.colId] = params.oldValue !== null && params.oldValue !== '' && parseFloat(params.oldValue) >= 0 ? params.oldValue : 0;
      params.api.refreshCells({
        rowNodes: [params.node],
        columns: [params.column.colId],
        force: true
      });
      useMessage().showMessage('warning', '输入值不能为负数');
      return;
    }

    // 如果是我们关注的字段，则进行计算
    if (['originalUnit', 'lossRate', 'unitConsumption'].includes(params.column.colId)) {
      updateCalculatedValues(params.data, params.column.colId, params.api, params.node);
    }
  },

  // 单元格编辑结束事件 - 当光标离开单元格时触发
  onCellEditingStopped: (params: any) => {
    // 当光标离开单元格时，立即计算并刷新
    if (params.data && params.column && ['originalUnit', 'lossRate', 'unitConsumption'].includes(params.column.colId)) {
      const originalUnit = Number(params.data.originalUnit || 0);
      const lossRate = Number(params.data.lossRate || 0);
      const unitConsumption = Number(params.data.unitConsumption || 0);

      // 根据编辑的字段进行相应的计算
      if (params.column.colId === 'originalUnit' || params.column.colId === 'lossRate') {
        // 如果编辑的是原始单耗或损耗率，计算单耗
        params.data.unitConsumption = calculateUnitConsumption(originalUnit, lossRate);
      } else if (params.column.colId === 'unitConsumption' && originalUnit > 0) {
        // 如果编辑的是单耗，计算损耗率
        params.data.lossRate = calculateLossRate(unitConsumption, originalUnit);
      }

      // 刷新所有相关单元格
      params.api.refreshCells({
        rowNodes: [params.node],
        columns: ['originalUnit', 'lossRate', 'unitConsumption'],
        force: true // 强制刷新
      });
    }
  },

  // 其他 AG Grid 选项
  editType: 'fullCell' as 'fullCell', // 使用单元格编辑模式，而不是整行编辑
  stopEditingWhenCellsLoseFocus: true, // 当单元格失去焦点时停止编辑
  singleClickEdit: true, // 单击编辑，使得编辑体验更流畅
  suppressClickEdit: false // 允许点击编辑
};


const fgCode = ref();

const addMCodeRef = ref();

const handleSave = async (data: any, node: any = null) => {
  // 确保数据中的计算字段是数字类型
  const numericFields = ['originalUnit', 'lossRate', 'unitConsumption', 'cusProvidedKit',
    'cusProvidedMopClothLength', 'cusProvidUnitConsumption', 'fullPageSets',
    'effectiveUsageRate', 'mopLength', 'casing'];

  const processedData = { ...data };

  // 将字符串转换为数字，并确保没有负数
  numericFields.forEach(field => {
    if (processedData[field] !== undefined && processedData[field] !== null) {
      if (typeof processedData[field] === 'string') {
        const value = parseFloat(processedData[field]);
        processedData[field] = value < 0 ? null : value;
      } else if (processedData[field] < 0) {
        processedData[field] = null;
      }
    }
  });

  // 确保单耗和损耗率的计算一致性
  if (processedData.originalUnit !== null && processedData.lossRate !== null) {
    processedData.unitConsumption = calculateUnitConsumption(
      processedData.originalUnit,
      processedData.lossRate
    );
  } else if (processedData.originalUnit !== null && processedData.unitConsumption !== null) {
    processedData.lossRate = calculateLossRate(
      processedData.unitConsumption,
      processedData.originalUnit
    );
  }

  try {
    // 保存到后端
    await updateTechConsumption(processedData);

    // 从后端重新获取最新数据
    //await getData({ fgCode: fgCode.value });

    // 显示成功消息
    useMessage().showMessage('success', $t('basic.UpdateSuccess'));

    // 如果提供了节点，则刷新该节点
    if (node && gridRef.value && gridRef.value.gridApi) {
      // 刷新整个表格
      gridRef.value.gridApi.refreshCells({
        force: true // 强制刷新所有单元格
      });
    }
  } catch (error) {
    console.error('Error saving data:', error);
    useMessage().showMessage('error', '保存失败');
  }
};

// 处理单耗计算的函数
const calculateUnitConsumption = (originalUnit: number | string, lossRate: number | string) => {
  const original = typeof originalUnit === 'string' ? parseFloat(originalUnit) : originalUnit;
  const loss = typeof lossRate === 'string' ? parseFloat(lossRate) : lossRate;

  if (isNaN(original) || isNaN(loss)) return 0;
  if (original < 0 || loss < 0) return null;

  // 单耗 = 原始单耗 * (1+损耗率)
  const result = original * (1 + loss);
  return result < 0 ? null : result;
};

// 处理损耗率计算的函数
const calculateLossRate = (unitConsumption: number | string, originalUnit: number | string) => {
  const unit = typeof unitConsumption === 'string' ? parseFloat(unitConsumption) : unitConsumption;
  const original = typeof originalUnit === 'string' ? parseFloat(originalUnit) : originalUnit;

  if (isNaN(unit) || isNaN(original) || original === 0) return 0;
  if (unit < 0 || original < 0) return null;

  // 损耗率 = 单耗/原始单耗 - 1
  const result = unit / original - 1;
  return result < 0 ? null : result;
};

const columnDefs: any[] = [
  { headerName: $t('production.RawMaterialCode'), field: 'mCode', width: 140 },
  { headerName: $t('production.RawMaterialName'), field: 'mName', width: 170 },
  { headerName: $t('production.InternalNo'), field: 'intCode', width: 140 },
  { headerName: $t('production.Casing'), field: 'casing', editable: (params: any) => params.data.id === editingRowId.value, cellEditor: 'agNumberCellEditor', width: 115 },
  { headerName: '✏️ ' + $t('production.MopLength'), field: 'mopLength', editable: (params: any) => params.data.id === editingRowId.value, cellEditor: 'agNumberCellEditor',  width: 140 },
  {
    headerName: '✏️ ' + $t('production.OriginalUnit'),
    field: 'originalUnit',
    editable: (params: any) => params.data.id === editingRowId.value,
    cellEditor: 'agNumberCellEditor',
    width: 140,
    cellEditorParams: {
      useFormatter: true
    }
  },
  {
    headerName: '✏️ ' + $t('production.LossRate'),
    field: 'lossRate',
    editable: (params: any) => params.data.id === editingRowId.value,
    cellEditor: 'agNumberCellEditor',
    width: 130,
    cellEditorParams: {
      useFormatter: true
    }
  },
  {
    headerName: '✏️ ' + $t('production.UnitConsumption'),
    field: 'unitConsumption',
    editable: (params: any) => params.data.id === editingRowId.value,
    cellEditor: 'agNumberCellEditor',
    width: 115,
    cellEditorParams: {
      useFormatter: true
    }
  },
  { headerName: '✏️ ' + $t('production.CusProvidedKit'), field: 'cusProvidedKit', editable: (params: any) => params.data.id === editingRowId.value, cellEditor: 'agNumberCellEditor', width: 140 },
  { headerName: '✏️ ' + $t('production.CusProvidedMopClothLength'), field: 'cusProvidedMopClothLength', editable: (params: any) => params.data.id === editingRowId.value, cellEditor: 'agNumberCellEditor', width: 165 },
  { headerName: '✏️ ' + $t('production.CusProvidUnitConsumption'), field: 'cusProvidUnitConsumption', editable: (params: any) => params.data.id === editingRowId.value, cellEditor: 'agNumberCellEditor', width: 140 },
  { headerName: '✏️ ' + $t('production.FullPageSets'), field: 'fullPageSets', editable: (params: any) => params.data.id === editingRowId.value, cellEditor: 'agNumberCellEditor', width: 140 },
  { headerName: '✏️ ' + $t('production.EffectiveUsageRate'), field: 'effectiveUsageRate', editable: (params: any) => params.data.id === editingRowId.value, cellEditor: 'agNumberCellEditor', width: 150 },
  {
    headerName: $t('production.Action'),

    field: "action", // 注意这里field不是实际的数据字段，仅用于标识
    pinned: 'right',
    cellRenderer: 'actionCell',
    width: 130,
    hide: props.isView,
    cellRendererParams: {
      actions: (params: any) => {
        if (params.data.id === editingRowId.value) {
          return [
            {
              label: $t('basic.save'),
              type: 'success',
              size: 'small',
              popConfirm: {
                title: $t('basic.confirmSave'),
                confirm: () => {
                  // 在保存前确保计算值的一致性
                  const originalUnit = Number(params.data.originalUnit || 0);
                  const lossRate = Number(params.data.lossRate || 0);

                  // 确保单耗和损耗率的计算一致性
                  params.data.unitConsumption = calculateUnitConsumption(originalUnit, lossRate);

                  // 刷新单元格显示
                  params.api.refreshCells({
                    rowNodes: [params.node],
                    columns: ['unitConsumption'],
                    force: true
                  });

                  // 停止编辑并保存
                  params.api.stopEditing();
                  handleSave(params.data, params.node);
                  editingRowId.value = null;
                }
              }
            },
            {
              label: $t('basic.cancel'),
              popConfirm: {
                title: $t('basic.confrimCancel'),
                confirm: () => {
                  const currentRow = rowData.value.find(row => row.id === editingRowId.value);

                  gridRef.value.gridApi.redrawRows({ rowNodes: [params.node] });
                  params.node.setData({ ...currentRow });
                  gridRef.value.gridApi.redrawRows({ rowNodes: [params.node] });
                  editingRowId.value = null;
                },
              },
              auth: ['tucfp.edit'],
              type: 'info',
              size: 'small',
            }
          ];
        }
        return [
          {
            label: $t('basic.edit'),
            callback: () => {
              editingRowId.value = params.data.id;
              //重绘
              gridRef.value.gridApi.redrawRows({ rowNodes: [params.node] });

              // 依次进入所有可编辑单元格
              gridRef.value.gridApi!.startEditingCell({
                rowIndex: params.node.rowIndex,
                colKey: "casing",
              });

            },
            auth: ['tucfp.edit'],
            type: 'primary',
            size: 'small',
          },
        ];
      }
    }
  },
];


// 格式化数字，保留4位小数
const formatNumber = (value: number | string | undefined | null) => {
  if (value === undefined || value === null || value === '') return '';
  const num = typeof value === 'string' ? parseFloat(value) : value;
  // 如果是负数，返回空字符串
  if (num < 0) return '';
  return num.toFixed(4);
};

const defaultColDef: any = {
  valueFormatter: (params: any) => {
    // 对数字类型的列应用格式化
    if (['originalUnit', 'lossRate', 'unitConsumption', 'cusProvidedKit',
         'cusProvidedMopClothLength', 'cusProvidUnitConsumption', 'fullPageSets',
         'effectiveUsageRate', 'mopLength', 'casing'].includes(params.colDef.field)) {
      // 如果值为null或负数，显示为空
      if (params.value === null || params.value === undefined || params.value === '' ||
          (typeof params.value === 'number' && params.value < 0) ||
          (typeof params.value === 'string' && parseFloat(params.value) < 0)) {
        return '';
      }
      return formatNumber(params.value);
    }
    return params.value;
  }
};
const rowSelection = ref({
  mode: "multiRow" as "multiRow",  // singleRow 默认单选//multiRow
  checkboxes: true,//默认关闭
  headerCheckbox: true, //默认关闭
  copySelectedRows: true,
});
const rowData = ref<any[]>([])
const handleAdd = (_data: any) => {
  if (!fgCode.value) {
    return;
  }
  addMCodeRef.value.setData({
    fgCode: fgCode.value
  });
  addMCodeRef.value.open();
  addMCodeRef.value.onClosed = () => {
    //调用刷新数据的方法
    getData({ fgCode: fgCode.value });
  };
}



const handleDelete = (_data: any) => {
  //获取到勾选数据
  const selectedRows = gridRef.value?.gridApi?.getSelectedRows();
  if (selectedRows.length === 0) {
    return;
  }
  useMessage().showMessageBox('confirm', $t('basic.confirmDelete'), $t('basic.tips'), 'warning', async (action) => {
    if (action === 'confirm') {
      //将勾选数据转换为ids，使用,拼接
      const ids = selectedRows.map((row: any) => row.id).join(',');
      //调用删除接口
      await deleteBomMCode({
        ids: ids,
      });
      await getData({ fgCode: fgCode.value });
      useMessage().showMessage('success', $t('production.DeleteSuccess'));
    }
  });
}



// 监听 id 变化，加载数据
watch(
  () => props.fgCode,
  async (newFgCode: string | undefined) => {
    if (newFgCode) {
      fgCode.value = newFgCode;
      await getData({ fgCode: newFgCode });
    }
  },
  { immediate: true }
);

</script>
<style>
</style>
