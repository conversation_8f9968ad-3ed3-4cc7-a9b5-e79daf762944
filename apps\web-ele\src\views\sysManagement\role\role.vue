<template>
  <PageCard auto-content-height>
    <QueryForm />

    <TableAction :actions="[
      {
        label: $t('production.Add'),
        type: 'primary',
        icon: 'ep:plus',
        auth: ['role.add'],
        onClick: openAdd.bind(null),
      }

    ]">
    </TableAction>

    <div style="margin-top: 20px; height: 100%;">
      <ClientGridComponent :columnDefs="columnDefs" :rowData="rowData" :pageSize="20" :defaultColDef="defaultColDef" />
    </div>


    <div>
      <createOrEdit />
    </div>
    <AuthMenu ref="authMenuRef" />

    <UserRole ref="userMenuRef" />
  </PageCard>




</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { TableAction } from '#/components/table-action';
import { useVbenForm } from '#/adapter/form';
import { PageCard, useVbenModal } from '@vben/common-ui';
import { sysRoleGet,deleteRole } from '#/api/sys/role'

import AuthMenu from './auth-menu.vue';
import roleCreateOrEdit from './roleCreateOrEdit.vue';
import UserRole from './user-Role.vue';

import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import { useMessage } from '#/components/elementPlus/useMessage';
import { $t } from '#/locales';

//import ActionCell from '@/components/ActionCell.vue';
const authMenuRef = ref();
const userMenuRef = ref();



async function getData(data: any | [] = []) {
  const res = await sysRoleGet({
    ...data
  });
  if (Array.isArray(res)) {
    // 确保 res 是数组类型
    rowData.value = res;
  }

}


function onRemove(data: any) {

  handleAuthMenu(data);
  // console.log(JSON.stringify(routes, null, 2));
}
const columnDefs: any[] = [
  { headerName: '角色代码', field: 'roleCode' },
  { headerName: '角色名称', field: 'roleName' },
  { headerName: '备注', field: 'remark' },
  { headerName: '描述', field: 'description' },
  {
    headerName: "操作",
    field: "action", // 注意这里field不是实际的数据字段，仅用于标识
    pinned: 'right',
    cellRenderer: 'actionCell',
    width: 500,
    cellRendererParams: {
      actions: [
        {
          label: '编辑',
          callback: (data: any) => {
            handleCreateOrEdit(data, 'edit');
          },
          type: 'primary',
          size: 'small',
          //eventName: 'editEvent' // 自定义事件名
        },
        {
          label: '删除',
          callback: (data: any) => {
            console.log('删除操作:', data);
            useMessage().showMessageBox('confirm', $t('basic.confirmDelete'), $t('basic.tips'), 'warning', async (action) => {
              if (action === 'confirm') {
                await deleteRole({
                  ids: data.id,
                });
                useMessage().showMessage('success', $t('production.DeleteSuccess'));

              }
            });
          },
          type: 'danger',
          size: 'small',
          //eventName: 'deleteEvent' // 自定义事件名
        },
        {
          label: '菜单授权',
          callback: (data: any) => {
            handleAuthMenu(data);
          },
          type: 'default',
          size: 'small',
        },
        {
          label: '成员管理',
          callback: (data: any) => {
            handleUserMenu(data);
          },
          type: 'default',
          size: 'small',
        },
      ]
    }
  },
];
const defaultColDef: any = {
  flex: 1

};
const rowData = ref<any[]>([])


const [QueryForm] = useVbenForm({
  handleSubmit: (values) => {
    getData(values);
  },
  submitButtonOptions: {
    content: '查询',
  },
  resetButtonOptions: {
    content: "重置",
  },
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  collapsed: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  schema: [
    {
      label: '角色代码',
      component: 'Input',
      fieldName: 'roleCode',
    },
    {
      label: '角色名称',
      component: 'Input',
      fieldName: 'roleName',
    },
    {
      label: '备注',
      component: 'Input',
      fieldName: 'remark',
    },
  ]
});

// 授权菜单
const handleAuthMenu = (record: any) => {
  authMenuRef.value.setData({
    record,
  });
  authMenuRef.value.open();
};
// 成员管理
const handleUserMenu = (record: any) => {
  userMenuRef.value.setData({
    record,
  });
  userMenuRef.value.open();
};

const handleCreateOrEdit = (data: any, type: string) => {
  createOrEditApi.setData({
    record: data,
    pageType: type,
  });
  createOrEditApi.open();
};

const [createOrEdit, createOrEditApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: roleCreateOrEdit,
});
function openAdd() {
  createOrEditApi.setData({
    pageType: 'add',
  });
  createOrEditApi.open();
}
onMounted(() => {
  getData();
});
</script>
