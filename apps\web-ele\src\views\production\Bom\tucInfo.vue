<script lang="ts" setup>
import { h, ref, watch } from 'vue';
import { useVbenForm } from '#/adapter/form';
import {
  ElMessageBox,
  ElNotification,
  ElButton as Button
} from 'element-plus';
import { $t } from '#/locales';
import { tucfpAddOrEdit, getTucfpInfo } from '#/api/production/bom';


import selectData from '#/views/common/selectData.vue';

const selectDataRef = ref();
const isUpdate = ref(false);
const loading = ref<boolean>(false);
const record = ref<any>({});

const selectConfig = {
  api: '/product/getProduct',
  columns: [
    { headerName: $t('product.productCode'), field: 'hsCode', width: 140 },
    { headerName: $t('product.productName'), field: 'productName', width: 140 },
    { headerName: $t('product.productDesc'), field: 'productDec', width: 140 },
    { headerName: $t('product.productUnit'), field: 'meterUnit', width: 155 },
    { headerName: $t('product.tradingUnit'), field: 'tradUnit', width: 135 },
    { headerName: $t('product.remark'), field: 'Remark', width: 135 },
  ],
  title: $t('basic.pleaseSelect'),
  showSearch: true,
  immediate: false,
  searchPlaceholder: $t('basic.selectInput'),
  multiple: false,
  class: "w-[50%] h-[70%] min-w-[600px]"
};


// 修改 selectOpen 函数
const selectOpen = async () => {
  try {
    selectDataRef.value.modalApi.open();
    // 监听弹窗关闭事件
    selectDataRef.value.modalApi.onClosed = () => {
      const selectedData = selectDataRef.value.modalApi.sharedData;
      if (selectedData && selectedData.length > 0) {
        const product = selectedData[0];
        // 更新表单数据
        formApi.setValues({
          productCode: product.hsCode,
          productName: product.productName,
          specModel: product.productDec,
          unit: product.meterUnit
        });
      }
    };
  } catch (error) {
    console.error('Error in selectOpen:', error);
    ElNotification({
      type: 'error',
      message: '打开选择窗口失败',
      duration: 2500
    });
  }
};


// 接收父组件传递的属性
const props = defineProps<{
  id?: string | number;
  isUpdate?: boolean;
  isView?: boolean;
  fgCode?: string;
  updateRecord: (newRecord: any) => void; // 接收 updateRecord 方法
}>();

const [AddForm, formApi] = useVbenForm({
  showDefaultActions: false,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
      placeholder: '',
      readonly: props.isView,
      // //disabled:props.isView,
    },
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-12 lg:grid-cols-12',
  schema: [
    {
      fieldName: 'id',
      label: 'ID',
      component: 'Input',
      disabled: true,
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      dependencies: {
        show: false,
        triggerFields: ['id'],
      },
    },
    {
      fieldName: 'fgCode',
      label: $t('production.ItemCode'),
      component: 'Input',
      disabled: true,
      //占满两列
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
        placeholder: null,
      },
    },
    {
      label: $t('production.ProductNature'),
      fieldName: "productNature",
      component: "Input",
      rules: 'required',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
    },
    {
      label: $t('production.Seat'),
      fieldName: "seat",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },

    // {
    //   label: $t('production.Invalid'),
    //   fieldName: "invalid",
    //   component: "Checkbox",
    //   formItemClass: 'col-span-2'
    // },
    {
      label: $t('production.SeriesNo'),
      fieldName: "seriesNo",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },
    {
      label: $t('production.ProdMatCode'),
      fieldName: "prodMatCode",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },
    {
      label: $t('production.StyleNo'),
      fieldName: "styleNo",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },

    {
      label: $t('production.TargetMarket'),
      fieldName: "targetMarket",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },
    {
      label: $t('production.VersionNo'),
      fieldName: "versionNo",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },
    {
      label: $t('production.UnitWeightKg'),
      fieldName: "unitWeightKg",
      component: "Input",
      formItemClass: 'col-span-3 items-start'
    },
    {
      label: $t('production.StorageMethod'),
      fieldName: "storageMethod",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },
    {
      label: $t('production.StorageCount'),
      fieldName: "storageCount",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },
    {
      label: $t('production.BoxWeight'),
      fieldName: "boxWeight",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },


    {
      label: $t('production.DomesticExport'),
      fieldName: "domesticExport",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },
    {
      label: $t('production.SalesCountry'),
      fieldName: "salesCountry",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
    },
    {
      label: $t('production.Valid'),
      fieldName: "valid",
      component: "Checkbox",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
    },
    {
      label: $t('production.ProdCode'),
      fieldName: "productCode",
      component: "SearchInput",
      formItemClass: 'col-start-1 col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
        searchButtonProps: {
          disabled: props.isView,
        },
        onSearch: async (e: Event) => {
          // 这里可以打开选择页面
          // 假设选择后返回的数据格式为 { value: 'xxx', text: 'xxx' }
          // 实际项目中需要根据您的选择页面实现来修改
          await selectOpen();
        },
      },
    },
    {
      label: $t('production.ProdName'),
      fieldName: "productName",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
    },
    {
      label: $t('production.Unit'),
      fieldName: "unit",
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3'
    },
    {
      label: $t('production.SpecModel'),
      fieldName: "specModel",
      component: "Input",
      componentProps: {
        type: 'textarea',
      },
      formItemClass: 'col-span-12'
    },
    {
      fieldName: 'remark',
      label: $t('production.Remark'),
      component: 'Input',
      componentProps: {
        type: 'textarea',
      },
      formItemClass: 'col-span-12',
    },
    {
      label: $t('production.Updater'),
      fieldName: "updateUserName",
      component: "Input",
      disabled: true,
       formItemClass: ' col-start-1 col-span-12 md:col-span-6 lg:col-span-4  2xl:col-span-3',
      componentProps: {
        placeholder: null,
      },
    },
    {
      label: $t('production.UpdateDate'),
      fieldName: "updateTime",
      component: "Input",
      disabled: true,
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4  2xl:col-span-3',
      componentProps: {
        placeholder: null,
      },
    },
    {
      label: $t('production.Reviewer'),
      fieldName: "reviewer",
      disabled: true,
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4  2xl:col-span-3',
      componentProps: {
        placeholder: null,
      },
    },
    {
      label: $t('production.ReviewDate'),
      fieldName: "reviewDate",
      disabled: true,
      component: "Input",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
        placeholder: null,
      },
    },
    {
      fieldName: 'btn',
      label: '',
      component: () => {
        return h(
          'div',
          {},
          h(
            Button,
            {
              type: 'primary',
              onClick: handleSave.bind(null),
              loading: loading.value,
              style: props.isView ? 'display: none' : ''
            },
            {
              default() {
                return $t('page.base.save');
              },
            },
          ),
        );
      },
      componentProps: {
      },
      formItemClass: 'col-start-1 md:col-span-6 lg:col-span-4  2xl:col-span-3',
    },
  ]
});

// 保存方法
const handleSave = () => {
  ElMessageBox.confirm(
    '确定要保存当前输入数据吗?',
    'Warning',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      formApi.validate().then(async (e: any) => {
        if (e.valid) {
          const values = await formApi.getValues();
          const res2 = await tucfpAddOrEdit(values);
          if (res2 != null) {
            // 更新 record 的值
            record.value.id = res2.id; // 假设 res2 是返回的 ID
            record.value.fgCode = res2.fgCode; // 更新 fgCode
          }


          // 调用父组件的 updateRecord 方法
          props.updateRecord(record.value); // 更新父组件的 record

          // const res = await getTucfpInfo({ id: res2.id });

          // formApi.setValues(res);
          ElNotification({ duration: 2500, message: $t('production.SaveSuccess'), type: 'success' });
        }
      });
    })
    .catch(() => {
      ElNotification({ duration: 2500, message: $t('production.CancelSuccess'), type: 'info' });
    });
};



// 监听 id 变化，加载数据
watch(
  () => props.id,
  async (newId) => {
    if (newId) {
      isUpdate.value = true;
      const res = await getTucfpInfo({ id: newId });
      formApi.setValues(res);
    } else {
      isUpdate.value = false;
      //formApi.reset();
    }
  },
  { immediate: true }
);
</script>

<template>
  <div class="p-4">
    <AddForm />
    <selectData ref="selectDataRef" v-bind="selectConfig" />
  </div>


</template>
